<!--
 * @Descripttion: 分部分项
 * @Author: sunchen
 * @Date: 2024-02-21 11:10:03
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-07-10 09:37:58
-->
<template>
  <div class="subItem-project custom-tree-table">
    <split
      horizontal
      ratio="4/3"
      :horizontal-bottom="35"
      style="height: 100%"
      mode="vertical"
      @onDragHeight="dragHeight">
      <template #one>
        <div class="table-content">
          <s-table
            size="small"
            ref="stableRef"
            class="s-table"
            :columns="showColumns"
            bordered
            :loading="loading"
            :delay="200"
            :rangeSelection="true"
            :scroll="{ y: stableHeight }"
            :custom-row="customRow"
            :custom-cell="customCell"
            :custom-header-cell="customHeaderCell"
            :rowClassName="(row, index) => rowClassName(row, index, tableData)"
            :animateRows="false"
            :pagination="false"
            :data-source="tableData"
            @closeEditor="setCloseEditor"
            @openEditor="openEditor"
            @mouseup="cellMouseup"
            @beforeOpenEditor="tableCellClickEvent"
            @cellKeydown="cellKeydown"
            @mousedown="mousedownHandle"
            @cellClick="sTableCellClickEvent">
            <!-- 自定义头部 -->
            <template #headerCell="{ title, column }">
              <!-- {{column}} -->
              <span class="custom-header" style="font-weight: bold">
                <i class="vxe-icon-edit" v-show="column.editable"></i>
                &nbsp;{{ title }}
                <CloseOutlined class="icon-close-s" @click="closeColumn({ column })" />
              </span>
            </template>
            <!--自定义内容 -->
            <template
              #bodyCell="{ text, record: row, index, column, key, openEditor, closeEditor }">
              <div v-if="column.field === 'checkbox'">
                <a-checkbox
                  :checked="rowSelection.selectedRowKeys.includes(row.sequenceNbr)"
                  @click="
                    e => {
                      const selected = e.target.checked;
                      rowSelection.onSelect(row, selected, [], e);
                    }
                  "></a-checkbox>
              </div>
              <div v-if="column.field === 'dispNo'">
                <span>{{ row.dispNo || '‎' }}</span>
              </div>
              <div
                class="cell-line-break-el"
                v-if="column.field === 'bdCode'"
                @dblclick="cellDBLClickEvent({ row, column }, '1')">
                <icon-font v-if="row.isLocked" type="icon-qingdan-suoding"></icon-font>
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 1 && tableData.length > 1"
                  class="vxe-icon-caret-down"></i>
                <i
                  @click.stop="changeStatus(row)"
                  v-if="row.displaySign === 2"
                  class="vxe-icon-caret-right"></i>
                <span class="code">
                  <a-tooltip>
                    <template #title>
                      {{ row.bdCode }} {{ row.redArray?.length > 0 ? row.redArray.join(',') : ''
                      }}{{ row.blackArray?.length > 0 ? row.blackArray.join(',') : '' }}
                    </template>
                    {{ row.bdCode }}
                    {{ row.redArray?.length > 0 ? row.redArray.join(',') : '' }}
                  </a-tooltip>
                </span>
                <span class="code-black" v-if="row.blackArray?.length > 0">
                  {{ row.blackArray.join(',') }}
                </span>
              </div>
              <div v-if="column.field == 'name'">
                <!-- :placement="index>=tableData.length-5?'rightBottom':'rightTop'" -->
                <a-popover
                  v-if="row.annotationsVisible"
                  placement="rightTop"
                  v-model:visible="row.annotationsVisible"
                  trigger="click"
                  overlayClassName="annotations-pop"
                  @visibleChange="val => visibleChange(val, row)"
                  :getPopupContainer="triggerNode => deNameRef(triggerNode)">
                  <template #content>
                    <div style="width: 250px; height: 140px">
                      <Annotations
                        @close="v => closeAnnotations(v, row)"
                        @onfocusNode="onFocusNode(row)"
                        :note="row.annotations"
                        style="left: 10px"
                        :isDisabled="row?.noteEditVisible"
                        :ref="el => getAnnotationsRef(el, row)"></Annotations>
                    </div>
                  </template>
                  <div
                    class="nameEdit"
                    @mouseover="cellMouseEnterEvent({ row, column })"
                    @mouseout="cellMouseLeaveEvent({ row, column })"
                    @dblclick="openEditor(key)">
                    {{ row.name }}
                    <icon-font
                      type="icon-bianji"
                      class="more-icon"
                      @click="
                        () => {
                          if (['主材费', '设备费'].includes(row.type)) {
                            openDEHangZCSB();
                          } else {
                            openEditDialog('name');
                          }
                        }
                      "></icon-font>
                  </div>
                </a-popover>
                <div v-else class="nameEdit" @mouseover="cellMouseEnterEvent({ row, column })">
                  <!-- <pre class="pre-name" v-html="row.name"></pre> -->
                  {{ row.name }}
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!row.isLocked"
                    @click="
                      () => {
                        if (['主材费', '设备费'].includes(row.type)) {
                          openDEHangZCSB();
                        } else {
                          openEditDialog('name');
                        }
                      }
                    "></icon-font>
                </div>
              </div>
              <div v-if="column.field == 'type'">
                <!-- <span
                  v-if="row.matchStatus === '1' && projectStore.combinedVisible"
                  class="flag-green"
                  >精</span
                >
                <span
                  v-if="row.matchStatus === '2' && projectStore.combinedVisible"
                  class="flag-orange"
                  >近</span
                >
                <span
                  v-if="row.matchStatus === '0' && projectStore.combinedVisible"
                  class="flag-red"
                  >未</span
                >
                <span
                  v-if="
                    (!row.borrowFlag && !row.changeFlag) || row.type === '费'
                  "
                  >{{ row.type }}</span
                ><span class="code-flag" v-if="row.type !== '费'"
                  >{{ row.changeFlag ? row.changeFlag : '' }}
                </span>
                <span
                  class="code-flag"
                  v-if="row.type !== '费' && !row.changeFlag"
                  >{{ row.borrowFlag ? row.borrowFlag : '' }}
                </span> -->
                <span :class="`${getTypeText(row).className}`">{{ getTypeText(row).text }}</span>
              </div>
              <!-- 项目特征 -->
              <template v-if="column.field == 'projectAttr'">
                <div class="nameEdit" v-if="row.kind == '03'">
                  <pre
                    v-if="projectStore.rowHeight"
                    class="pre-name"
                    v-html="row.projectAttr"></pre>
                  <div v-else class="surely-table-cell-text-ellipsis">
                    {{ row.projectAttr }}
                  </div>
                  <icon-font
                    type="icon-bianji"
                    class="more-icon"
                    v-show="!row.isLocked"
                    @click="openEditDialog('projectAttr')"></icon-font>
                </div>
              </template>
              <!-- 规格型号 -->
              <template v-if="column.field == 'specification'">
                <div class="nameEdit">
                  <pre class="pre-name" v-html="row.specification"></pre>
                </div>
              </template>
              <!-- coldResistantSuborder防寒子目 -->
              <template v-if="column.field === 'coldResistantSuborder'">
                <a-checkbox
                  v-if="row.kind === '04' && projectStore.deStandardReleaseYear === '12'"
                  v-model:checked="row.coldResistantSuborder"
                  @change="updateFbData(row, 'coldResistantSuborder')"></a-checkbox>
              </template>
              <!-- 工程量表达式 -->
              <template v-if="column.field == 'quantityExpression'">
                <div class="nameEdit">
                  <span class="name">
                    {{ !row.bdCode && row.kind === '04' ? 0 : row.quantityExpression }}
                  </span>
                  <icon-font
                    v-show="['03', '04'].includes(row.kind) && !row.isLocked"
                    type="icon-bianji"
                    class="more-icon"
                    @click="openEditDialog('quantityExpression')"></icon-font>
                </div>
              </template>
              <template v-if="column.field == 'quantityEdit'">
                <span>{{ row.originalQuantity }}</span>
              </template>
              <template v-if="column.field == 'lockPriceFlag'">
                <a-checkbox
                  :indeterminate="
                    (row.kind === '0' || row.kind === '01' || row.kind === '02') &&
                    row.parentLockPriceFlag &&
                    !row.lockPriceFlag
                  "
                  v-if="['0', '01', '02', '03'].includes(row.kind) && !row.tempDeleteFlag"
                  v-model:checked="row.lockPriceFlag"
                  @change="updateFbData(row, 'lockPriceFlag')"></a-checkbox>
              </template>
              <!-- ifProvisionalEstimate -->
              <template v-if="column.field == 'ifProvisionalEstimate'">
                <a-checkbox
                  v-if="[94, 95].includes(row.kind) || (row.kind === '04' && row.rcjFlag)"
                  :checked-value="1"
                  :unchecked-value="0"
                  v-model:checked="row.ifProvisionalEstimate"
                  @change="
                    [94, 95].includes(currentInfo.kind)
                      ? updateConstructRcj(row, 'ifProvisionalEstimate', null)
                      : updateFbData(row, null, {
                          ifProvisionalEstimate: row.ifProvisionalEstimate,
                        })
                  "></a-checkbox>
              </template>
              <template v-if="column.field == 'zjfPrice'">
                <span v-if="!['04', 94, 95].includes(row.kind)">{{}}</span>
                <span v-else>{{ row.zjfPrice }}</span>
              </template>
              <template v-if="column.field == 'zjfTotal'">
                <span v-if="!['04', 94, 95].includes(row.kind)"></span>
                <span v-else>{{ row.zjfTotal }}</span>
              </template>
              <template v-if="column.field == 'price'">
                <!-- <span v-if="row.kind !== '04'">{{}}</span> -->
                <!-- bug14554-清单行展示综合单价 -->
                <span
                  :class="{
                    priceFlag:
                      row.ceilingPrice && row.price > row.ceilingPrice && row.kind === '03',
                  }">
                  {{ row.price }}
                </span>
              </template>
              <template v-if="column.field == 'total'">
                <span
                  v-if="![94, 95].includes(row.kind)"
                  :class="{
                    priceFlag:
                      row.ceilingPrice &&
                      row.total > row.ceilingPrice &&
                      (row.kind === '01' || row.kind === '02'),
                  }">
                  {{ row.total }}
                </span>
                <span v-else></span>
              </template>
              <template v-if="column.field == 'qfCode'">
                <span :title="row.qfName">{{ row.qfName }}</span>
              </template>
              <template v-if="column.field == 'ifMainQd' && !['04', 94, 95].includes(row.kind)">
                <a-checkbox
                  :indeterminate="
                    (row.kind === '0' || row.kind === '01' || row.kind === '02') &&
                    row.mainQdParentAllCheckFlag &&
                    !row.ifMainQd
                  "
                  v-model:checked="row.ifMainQd"
                  @change="updateFbData(row, 'ifMainQd')"></a-checkbox>
              </template>

              <template v-if="column.field == 'ceilingPrice'">
                <span v-if="['01', '02', '03'].includes(row.kind)">{{ row.ceilingPrice }}</span>
              </template>
              <template v-if="['description', 'costMajorName'].includes(column.field)">
                {{ row[column.field] }}
              </template>
              <template v-if="column.field == 'measureType'">
                {{ row[column.field] }}
              </template>
            </template>
            <!--自定义编辑 -->
            <template
              #cellEditor="{
                column,
                modelValue,
                save,
                closeEditor,
                record: row,
                editorRef,
                getPopupContainer,
                recordIndexs,
              }">
              <template v-if="column.field == 'name'">
                <!-- <Annotations
                  @close="v => closeAnnotations(v, row)"
                  @onfocusNode="onFocusNode(row)"
                  v-if="
                    row?.noteViewVisible ||
                    row?.isShowAnnotations ||
                    row?.noteEditVisible
                  "
                  :note="row.annotations"
                  :isDisabled="row?.noteEditVisible || row?.isShowAnnotations"
                  :ref="el => getAnnotationsRef(el, row)"
                ></Annotations> -->
                <bdNameSelect
                  v-if="['01', '02'].includes(row.kind)"
                  :modelValue="modelValue.value"
                  @update:modelValue="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row.name);
                      closeEditor();
                    }
                  "></bdNameSelect>

                <vxe-pulldown
                  ref="bdNamePulldownRef"
                  transfer
                  v-else-if="['03'].includes(row.kind)">
                  <template #default>
                    <a-textarea
                      className="custom-input"
                      placeholder="请输入项目名称"
                      :ref="el => setInputRef(el, 'name')"
                      @compositionstart="onCompositionStart(row)"
                      @compositionend="onCompositionEnd(row, modelValue.value)"
                      @change="e => bdNameKeyupEvent(row, modelValue.value)"
                      :rows="1"
                      :bordered="false"
                      :value="modelValue.value"
                      @update:value="
                        v => {
                          modelValue.value = v;
                        }
                      "
                      @blur="
                        () => {
                          timeCLose(closeEditor, { row, column }, modelValue.value, row.name);
                        }
                      " />
                  </template>
                  <template #dropdown>
                    <div class="my-dropdown4">
                      <vxe-grid
                        border
                        auto-resize
                        :show-header="false"
                        height="auto"
                        width="500"
                        :row-config="{ isHover: true }"
                        :data="bdNameTableList"
                        :columns="tableColumn"
                        @cell-click="
                          ({ row }) => {
                            (isNameOpen = true), dbNameCellClickEvent({ row });
                          }
                        "></vxe-grid>
                    </div>
                  </template>
                </vxe-pulldown>

                <a-textarea
                  v-else
                  className="custom-input"
                  placeholder="请输入项目名称"
                  :ref="el => setInputRef(el, 'name')"
                  :rows="1"
                  :bordered="false"
                  :value="modelValue.value"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      timeCLose(closeEditor, { row, column }, modelValue.value, row.name);
                    }
                  " />
              </template>
              <template v-if="column.field == 'bdCode'">
                <vxe-pulldown
                  ref="pulldownRef"
                  transfer
                  v-if="[94, 95, '03', '04'].includes(row.kind)"
                  class-name="bdCodeContent">
                  <template #default>
                    <a-input
                      :bordered="false"
                      clearable
                      v-if="!row.isLocked"
                      :ref="el => setInputRef(el, column.field)"
                      :value="modelValue.value"
                      placeholder="请输入项目编码"
                      @keyup="keyupEvent(row, modelValue.value)"
                      :get-popup-container="getPopupContainer"
                      @click="cellDBLClickEvent({ row, column })"
                      @dblclick="cellDBLClickEvent({ row, column }, '1')"
                      @update:value="
                        v => {
                          modelValue.value = v;
                        }
                      "
                      @blur="
                        () => {
                          timeCLose(closeEditor, { row, column }, modelValue.value, row.bdCode);
                        }
                      " />
                    <span
                      style="display: inline-block; width: 100%; height: 100%"
                      v-else
                      @dblclick="cellDBLClickEvent({ row, column }, '1')">
                      {{ row.bdCode }}
                    </span>
                  </template>
                  <template #dropdown>
                    <div class="my-dropdown4" v-if="row.kind === '03'">
                      <vxe-grid
                        border
                        auto-resize
                        :show-header="false"
                        height="auto"
                        width="500"
                        :row-config="{ isHover: true }"
                        :loading="loading"
                        :data="tableList"
                        :columns="tableColumn"
                        @cell-click="cellClickEvent"></vxe-grid>
                    </div>
                  </template>
                </vxe-pulldown>
                <template v-else-if="['02', '01'].includes(row.kind)">
                  <a-input
                    :bordered="false"
                    clearable
                    v-if="!row.isLocked"
                    :ref="el => setInputRef(el, column.field)"
                    :value="modelValue.value"
                    placeholder="请输入项目编码"
                    @click="cellDBLClickEvent({ row, column })"
                    @dblclick="cellDBLClickEvent({ row, column }, '1')"
                    @update:value="
                      v => {
                        modelValue.value = v;
                      }
                    "
                    @blur="
                      () => {
                        timeCLose(closeEditor, { row, column }, modelValue.value, row.bdCode);
                      }
                    " />
                  <span
                    style="display: inline-block; width: 100%; height: 100%"
                    v-else
                    @dblclick="cellDBLClickEvent({ row, column }, '1')">
                    {{ row.bdCode }}
                  </span>
                </template>
                <template v-else>
                  <span class="code" @dblclick="cellDBLClickEvent({ row, column }, '1')">
                    <i
                      @click.stop="changeStatus(row)"
                      v-if="row.displaySign === 1 && tableData.length > 1"
                      class="vxe-icon-caret-down"></i>
                    <i
                      @click.stop="changeStatus(row)"
                      v-if="row.displaySign === 2"
                      class="vxe-icon-caret-right"></i>
                    {{ row.bdCode }}
                  </span>
                </template>
              </template>

              <!-- 项目特征 -->
              <template v-if="column.field == 'projectAttr'">
                <vxe-pulldown ref="pulldownRefAttr" transfer>
                  <template #default>
                    <div @click="keyupAttrEvent(row)">
                      <a-textarea
                        :maxlength="2000"
                        :clearable="false"
                        @focus="(projectAttrFocus(row), keyupAttrEvent(row))"
                        @change="projectAttrChange(row)"
                        :ref="el => setInputRef(el, column.field)"
                        :bordered="false"
                        :value="modelValue.value"
                        @update:value="
                          v => {
                            modelValue.value = v;
                          }
                        "
                        @blur="
                          () => {
                            // if (associationVisible) return;
                            timeCLose(
                              closeEditor,
                              { row, column },
                              modelValue.value,
                              row.projectAttr,
                              800
                            );
                          }
                        "></a-textarea>
                    </div>
                  </template>
                  <template #dropdown>
                    <project-attr-association
                      ref="associationRef"
                      v-model:isNameOpen="isNameOpen"
                      class="association-popover"
                      :style="{ width: '280px', margin: '0px 0 0 -5px' }"
                      @dblclickHandler="
                        data => {
                          modelValue.value = data.projectAttr;
                          associationDblClick(data, row);
                          timeCLose(
                            closeEditor,
                            { row, column },
                            modelValue.value,
                            row.projectAttr,
                            800
                          );
                        }
                      " />
                  </template>
                </vxe-pulldown>
              </template>
              <!-- 类型 -->
              <template v-if="column.field === 'type'">
                <a-select
                  :value="row.type"
                  :options="row.typeList"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :field-names="{ label: 'desc', value: 'desc' }"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.unit);
                      closeEditor();
                    }
                  "></a-select>
              </template>
              <!-- 单位 -->
              <template v-if="column.field == 'unit'">
                <a-select
                  size="small"
                  v-if="unitLimit(row)"
                  :bordered="false"
                  :show-search="false"
                  :notFoundContent="null"
                  :ref="el => setInputRef(el, 'unit')"
                  :showArrow="false"
                  :value="modelValue.value"
                  :filter-option="
                    input => {
                      modelValue.value = input;
                    }
                  "
                  :options="'项'.split('/').map(item => ({ label: item, value: item }))"
                  open
                  @blur="
                    (editClosedEvent({ row, column }, modelValue.value, row.unit), closeEditor())
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.unit);
                      closeEditor();
                    }
                  "></a-select>
                <a-select
                  size="small"
                  v-if="
                    !unitLimit(row) && row.kind === '03' && row.bdCode && row.isSupplement !== 1
                  "
                  :bordered="false"
                  show-search
                  :notFoundContent="null"
                  :ref="el => setInputRef(el, 'unit')"
                  :showArrow="false"
                  :value="modelValue.value"
                  :filter-option="
                    input => {
                      modelValue.value = input;
                    }
                  "
                  :options="row.unitList?.split('/').map(item => ({ label: item, value: item }))"
                  open
                  @blur="
                    (editClosedEvent({ row, column }, modelValue.value, row.unit), closeEditor())
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.unit);
                      closeEditor();
                    }
                  "></a-select>
                <!-- 编辑区单位-支持编辑 -->
                <a-select
                  size="small"
                  v-if="
                    (!unitLimit(row) && ['04', 94, 95].includes(row.kind) && row.bdCode) ||
                    row.isSupplement === 1
                  "
                  :bordered="false"
                  show-search
                  :notFoundContent="null"
                  :ref="el => setInputRef(el, 'unit')"
                  :showArrow="false"
                  :value="modelValue.value"
                  :filter-option="
                    input => {
                      modelValue.value = input;
                    }
                  "
                  :options="
                    projectStore.unitListString.split(',').map(a => {
                      return { value: a, label: a };
                    })
                  "
                  open
                  @blur="
                    (editClosedEvent({ row, column }, modelValue.value, row.unit), closeEditor())
                  "
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.unit);
                      closeEditor();
                    }
                  "></a-select>
              </template>

              <!-- 工程量表达式 -->
              <template v-if="column.field == 'quantityExpression'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row[column.field]);
                      closeEditor();
                    }
                  "></a-input>
              </template>
              <!-- 规格型号 -->
              <template v-if="column.field === 'specification'">
                <a-input
                  :bordered="false"
                  v-if="isSpecificationEdit(row)"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :maxlength="50"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row[column.field]);
                      closeEditor();
                    }
                  " />
                <span v-else>{{ row.specification }}</span>
              </template>
              <!-- 工程量 -->
              <template v-if="column.field == 'quantityEdit'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  :value="modelValue.value"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row['quantityExpression']);
                      closeEditor();
                    }
                  " />
              </template>
              <!-- 单价 -->
              <!--:value="row.zjfPriceFormula"-->
              <template v-if="column.field == 'zjfPrice'">
                <a-input
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  v-model:value="tableData[recordIndexs[0]].zjfPriceFormula"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      //modelValue.value = (v + '').replace(/[^\d.]/g, '');
                    }
                  "
                  @blur="
                    v => {
                      //添加四则运算逻辑
                      const flag = editBlur(
                        row,
                        'zjfPriceFormula',
                        v.target.value,
                        row[column.field]
                      );
                      if (flag) {
                        editClosedEvent({ row, column }, v.target.value, row[column.field]);
                      }
                      closeEditor();
                      // modelValue.value = pureNumber(v.target.value, 2);
                      // editClosedEvent( { row, column },  modelValue.value,  row[column.field] );
                      // closeEditor();
                    }
                  " />
              </template>
              <!-- 单价构成文件 -->
              <template v-if="column.field == 'qfCode'">
                <a-select
                  :value="row.qfCode"
                  :options="djgcFileList"
                  :field-names="{ label: 'qfName', value: 'qfCode' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.qfCode);
                      closeEditor();
                    }
                  "></a-select>
              </template>
              <!-- 取费文件 -->
              <template v-if="column.field == 'costMajorName'">
                <a-select
                  :value="row.costFileCode"
                  :options="feeFileList"
                  :field-names="{ label: 'qfName', value: 'qfCode' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.costFileCode);
                      closeEditor();
                    }
                  "></a-select>
              </template>
              <!-- 施工组织措施类别 -->
              <template v-if="column.field == 'measureType'">
                <a-select
                  :value="row.measureType"
                  :options="szTypeList"
                  :field-names="{ label: 'cslbName', value: 'cslbName' }"
                  :bordered="false"
                  :ref="el => setInputRef(el, column.field)"
                  size="small"
                  open
                  @blur="closeEditor()"
                  className="custom--inner"
                  transfer
                  @update:value="
                    v => {
                      modelValue.value = v;
                      editClosedEvent({ row, column }, modelValue.value, row.measureType);
                      closeEditor();
                    }
                  "></a-select>
              </template>
              <!-- 备注 -->
              <template v-if="column.field == 'description'">
                <a-textarea
                  :maxlength="2000"
                  :clearable="false"
                  @focus="projectAttrFocus(row)"
                  @change="projectAttrChange(row)"
                  :ref="el => setInputRef(el, column.field)"
                  :bordered="false"
                  :value="modelValue.value"
                  @update:value="
                    v => {
                      modelValue.value = v;
                    }
                  "
                  @blur="
                    () => {
                      editClosedEvent({ row, column }, modelValue.value, row[column.field]);
                      closeEditor();
                    }
                  "></a-textarea>
              </template>
              <!-- 最高限价 -->
              <template v-if="column.field == 'ceilingPrice'">
                <a-input
                  v-if="row.kind === '01' || row.kind === '02' || row.kind === '03'"
                  :value="modelValue.value"
                  :ref="el => setInputRef(el, column.field)"
                  class="ceilingPrice-wrap"
                  :bordered="false"
                  :get-popup-container="getPopupContainer"
                  @update:value="
                    v => {
                      modelValue.value = (v + '').replace(/[^\d.]/g, '');
                    }
                  "
                  @blur="
                    v => {
                      modelValue.value = costPriceFormat(v.target.value);
                      editClosedEvent({ row, column }, modelValue.value, row[column.field]);
                      closeEditor();
                    }
                  " />
                <span v-else>
                  {{ row.ceilingPrice }}
                </span>
              </template>
            </template>
            <!-- 右键菜单 -->
            <template #contextmenuPopup="args">
              <sub-menu
                :selectState="selectState"
                :args="args"
                :copyData="copyData"
                :tableData="tableData"
                type="fbfx"
                :hangMenuList="hangMenuList"
                :deleteStateFn="deleteStateFn"
                @handleNote="handleNote"
                @handleMainList="handleMainList"
                @hangMenuDisabledHandler="hangMenuDisabledHandler"
                @contextMenuClickEvent="
                  ({ menu, row }) => contextMenuClickEvent({ menu, row }, args)
                "
                v-model:currentInfo="currentInfo"></sub-menu>
            </template>
          </s-table>
        </div>
      </template>
      <template #two>
        <div class="quota-content">
          <quota-info
            ref="quotaInfoRef"
            :currentInfo="currentInfo"
            :isAttrContent="isAttrContent"
            :type="1"
            :fatherLoading="tableLoading"
            :isUpdateFile="isUpdateFile"
            :isUpdateQuantities="isUpdateQuantities"
            @refreshCurrentInfo="refreshCurrentInfo"
            @tabClickBefore="showInfo"
            @onDbClickFile="onDbClickFile"
            :isComplete="isComplete"
            :isUpdate="isUpdate"></quota-info>
        </div>
      </template>
    </split>
    <inventory-and-quota-index
      v-model:indexVisible="indexVisible"
      @currentQdDeInfo="currentQdDeInfo"
      @currentInfoReplace="currentInfoReplace"
      :defaultValue1="defaultValue1"
      :indexLoading="indexLoading"
      :originInfo="currentInfo"
      v-model:checkBatchOption="checkBatchOption"></inventory-and-quota-index>
    <common-modal
      v-model:modelValue="deleteVisible"
      className="dialog-comm"
      title="删除操作"
      width="400px">
      <div class="content" v-if="deleteList.length === 2">请选择删除范围？</div>
      <div class="content" v-if="deleteList[0] === 5">
        是否确定删除？将删除{{ currentInfo.kind === '01' ? '分部' : '清单' }}及其下挂所有数据.
      </div>
      <div class="content" v-if="deleteList.length === 1 && deleteList[0] === 4">
        是否确定删除？
      </div>
      <div class="footer-btn-list">
        <a-button
          v-if="deleteList.length === 2 || isShowDelNow"
          @click="delFbData(false)"
          :disabled="deleteLoading">
          删除当前行
        </a-button>
        <a-button
          v-if="deleteList.length === 2 || isShowDelNow"
          type="primary"
          @click="delFbData(true)"
          :disabled="deleteLoading">
          删除关联数据
        </a-button>
        <div class="content" v-if="isBatchDelete && !isShowDelNow">是否确定删除选中所有数据？</div>
        <a-button
          v-if="(deleteList.length === 1 || isBatchDelete) && !isShowDelNow"
          type="primary"
          ghost
          @click="cancel">
          取消
        </a-button>
        <a-button
          v-if="(deleteList.length === 1 || isBatchDelete) && !isShowDelNow"
          type="primary"
          @click="delFbData(currentInfo.kind !== '04')"
          :disabled="deleteLoading">
          确定
        </a-button>
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="delBlankRow.visible"
      className="dialog-comm"
      title="清除空行"
      width="400px">
      <div class="delBlankModal">
        <div class="single">
          <div class="title">
            <icon-font class="icon-font" type="icon-anzhuangfeiyongshuchufangshi"></icon-font>
            选择范围
          </div>
          <a-radio-group v-model:value="delBlankRow.val" style="margin: 0px 0 0">
            <a-radio :value="1">当前单位工程</a-radio>
            <a-radio :value="2" style="margin-left: 20px">整个项目</a-radio>
          </a-radio-group>
        </div>
        <div class="footor">
          <p>
            <icon-font type="icon-querenshanchu" class="iconFont"></icon-font>
            删除空分部，空清单，空子目及空的措施项目行，是否继续
          </p>
          <span class="btn-list">
            <a-button @click="initDelBlankRow()">否</a-button>
            <a-button style="margin-left: 10px" type="primary" @click="delBlankRowFun(1)">
              是
            </a-button>
          </span>
        </div>
      </div>
    </common-modal>
    <common-modal
      v-model:modelValue="isShowModel"
      className="dialog-comm"
      :title="showModelTitle"
      width="700"
      height="350">
      <a-textarea
        v-model:value="editContent"
        placeholder="请输入编辑内容"
        :rows="8"
        @change="modelTextareaChange"
        class="edit-content" />
      <div class="footer-btn-list">
        <a-button @click="editCancel">取消</a-button>
        <a-button type="primary" @click="saveContent()">确定</a-button>
      </div>
    </common-modal>

    <info-modal
      v-model:infoVisible="infoVisible"
      :infoText="infoText"
      :isSureModal="isSureModal"
      :iconType="iconType"
      @updateCurrentInfo="updateCurrentInfo"></info-modal>
    <bcQd
      v-model:qdVisible="qdVisible"
      :code="bdCode"
      :type="1"
      :currentInfo="currentInfo"
      @saveData="saveData"
      @bcCancel="bcCancel"></bcQd>
    <bcDe
      v-model:visible="deVisible"
      :code="bdCode"
      :type="1"
      :currentInfo="currentInfo"
      @deSaveData="deSaveData"
      @bcCancel="bcCancel"></bcDe>
    <bcRcj
      :code="bdCode"
      v-model:visible="rcjVisible"
      :currentInfo="currentInfo"
      @rcjSaveData="rcjSaveData"
      @bcCancel="bcCancel"></bcRcj>
    <common-modal
      v-model:modelValue="isPriceModel"
      className="dialog-comm"
      :title="showPriceTitle"
      :width="showModelType === 'csfy' ? '1000' : showModelType === 'azfy' ? 1200 : 800"
      :destroyOnClose="true"
      height="auto"
      @close="closePriceModel">
      <keep-alive>
        <component
          :is="components.get(showModelType)"
          @updateData="updateData"
          @close="closePriceModel"></component>
      </keep-alive>
    </common-modal>
    <div class="unit-radio" v-if="showUnitTooltip">
      <div class="title">选择单位</div>
      <a-radio-group v-model:value="selectUnit">
        <a-radio v-for="(unit, index) in addCurrentInfo?.unit" :key="index" :value="unit">
          {{ unit }}
        </a-radio>
      </a-radio-group>
      <a-button type="link" @click="selectHandler(addCurrentInfo)">确定</a-button>
    </div>
    <set-standard-type
      v-if="standardVisible"
      v-model:standardVisible="standardVisible"
      :type="1"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      @refreshCurrentInfo="refreshCurrentInfo"
      @closeDialog="closeDialog"></set-standard-type>
    <set-main-material
      v-if="materialVisible"
      v-model:materialVisible="materialVisible"
      :indexLoading="indexLoading"
      :currentInfo="addDeInfo"
      :type="1"
      :mainMaterialTableData="mainMaterialTableData"
      @setUpdate="setUpdate"></set-main-material>
    <!-- 第一个弹框-组价匹配的 -->
    <common-modal
      v-model:modelValue="comMatchModal"
      className="dialog-comm"
      title="组价方案匹配"
      width="600"
      height="400"
      :destroyOnClose="true">
      <component-matching @closeComMatch="closeComMatch"></component-matching>
    </common-modal>
    <CodeReset
      v-model:visible="codeResetVisible"
      @refreshCallback="
        () => {
          page = 1;
          queryBranchDataById();
        }
      "></CodeReset>
    <schedule-file
      v-model:dialogVisible="showSchedule"
      strokeColor="#54a1f3"
      :percent="percent"
      :desc="percentInfo?.dec"
      :pageType="'comMatch'"
      @isContinue="isContinue"
      :isNoClose="isNoClose"
      :percentInfo="percentInfo"
      :width="600"></schedule-file>
    <common-modal
      className="titleNoColor noHeaderHasclose"
      v-model:modelValue="resetModal"
      title=" "
      width="400"
      height="200">
      <div class="reCheck">
        <p style="font-weight: 600">
          <icon-font class="icon" type="icon-querenshanchu" style="margin-right: 5px" />
          组价进行中，是否确定关闭？
        </p>
        <p style="padding-left: 20px; margin-bottom: 26px">当前数据已发生变化是否应用组价后数据</p>

        <a-button style="margin: 0 30px 0 20px" @click="recover(true)">
          否，恢复至组价前数据
        </a-button>
        <a-button type="primary" @click="recover(false)">确定</a-button>
      </div>
    </common-modal>
    <!-- 饼图-组价匹配的 -->
    <common-modal
      className="dialog-comm"
      title="组价方案匹配"
      width="550"
      height="400
    "
      v-model:modelValue="reportModel"
      @cancel="reportModel = false"
      @close="reportModel = false">
      <match-pic @lookView="lookView" :startMatchData="startMatchData"></match-pic>
    </common-modal>
    <!--    <combined-search @filterData="filterData"></combined-search>-->

    <!-- 复用组价 -->
    <ReuseGroupPriceDialog
      ref="ReuseGroupPriceRef"
      @refresh="queryBranchDataById('Refresh', '', false)"
      :currentInfo="currentInfo"
      :lockBtnStatus="lockBtnStatus"></ReuseGroupPriceDialog>

    <!-- 清单快速组价 -->
    <qdQuickPricing
      ref="qdQuickPricingRef"
      @refresh="queryBranchDataById('Refresh', '', false)"
      @posRow="posRow"
      :currentInfo="currentInfo"
      :lockBtnStatus="lockBtnStatus"></qdQuickPricing>

    <batch-delete
      v-model:batchDeleteVisible="batchDeleteVisible"
      :batchDataType="batchDataType"
      @updateData="queryBranchDataById"></batch-delete>
    <specificItem
      v-model:batchDeleteVisible="batchDeleteSubItemsVisible"
      :batchDataType="batchDataType"
      @updateData="queryBranchDataById" />

    <!-- 整理子目 分布整理 清单排序-->
    <component
      :is="components.get(subitemIdentification)"
      v-model:subitemIdentification="subitemIdentification"
      @updateData="
        () => {
          emits('updateMenuList');
          queryBranchDataById();
        }
      " />

    <PageColumnSetting
      :columnOptions="handlerColumns"
      ref="columnSettingRef"
      title="页面显示列设置"
      @save="updateColumns"
      :getDefaultColumns="getDefaultColumns" />
    <standard-group-price ref="standardGroupRef" :currentInfo="currentInfo"></standard-group-price>

    <areaModal v-if="areaStatus" :type="areaVisibleType" @closeDialog="closeAreaModal"></areaModal>
    <DEHangZCSB
      v-model:visible="DEHangZCSBVisible"
      :currentInfo="currentInfo"
      @updateData="
        rcjParams => {
          if ([94, 95].includes(currentInfo.kind)) {
            updateConstructRcj(currentInfo, null, rcjParams);
          } else {
            updateFbData(currentInfo, null, rcjParams);
          }
        }
      "></DEHangZCSB>
    <syncNameToDE
      v-model:visible="syncToDEVisible"
      :currentInfo="currentInfo"
      @updateData="updateFbData"></syncNameToDE>
    <material-machine-index
      v-model:indexVisible="rcjIndexVisible"
      :currentMaterialInfo="currentInfo"
      :indexLoading="rcjIndexLoading"
      @addChildrenRcjData="
        row => {
          addChildrenRcjData(row, currentInfo);
        }
      "
      @currentInfoReplace="
        row => {
          retailAreaRcjReplace(row, currentInfo);
        }
      "></material-machine-index>
    <AssociateSubQuotas
      v-if="associateSubQuotasVisible"
      v-model:visible="associateSubQuotasVisible"
      :currentInfo="addDeInfo"
      :type="1"
      @successHandler="successHandler"
      @quotasCancel="quotasCancel"></AssociateSubQuotas>
    <PartialSummary ref="partialSummaryRef"></PartialSummary>
  </div>
</template>

<script setup>
import {
  onMounted,
  onBeforeUnmount,
  reactive,
  ref,
  watch,
  nextTick,
  markRaw,
  defineAsyncComponent,
  onActivated,
  onDeactivated,
  getCurrentInstance,
  toRaw,
} from 'vue';
import { checkisOnline } from '@/utils/publicInterface';
import QuotaInfo from '../quotaInfo/index.vue';
import InventoryAndQuotaIndex from '../inventoryAndQuotaIndex/index.vue';
import api from '../../../../api/projectDetail.js';
import { projectDetailStore } from '../../../../store/projectDetail';
import ComponentMatching from '../measuresItem/componentMatching.vue';
import MatchPic from '../measuresItem/MatchPic.vue';
import { message } from 'ant-design-vue';
import { ItemBillMenuOperator } from './ItemBillMenuOperator';
import xeUtils from 'xe-utils';
import split from '@/components/split/index.vue';
import { insetBus } from '@/hooks/insetBus';
import operateList from '../operate';
import { useCheckBefore } from '@/hooks/useCheckBefore';
import infoMode from '../../../../plugins/infoMode';
import SetStandardType from '../quotaInfo/setStandardType.vue';
import SetMainMaterial from '../quotaInfo/setMainMaterial.vue';
import ProjectAttrAssociation from '@/components/ProjectAttrAssociation/ProjectAttrAssociation.vue';

import { useCellClick } from '@/hooks/useCellClick';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import { useAttrAssociation } from '@/hooks/useAttrAssociationStable.js';
import { onClickOutside } from '@vueuse/core';
import { useSubItem } from '@/hooks/useSubItemStable.js';
import { CloseOutlined } from '@ant-design/icons-vue';
import { useFormatTableColumns } from '@/hooks/useFormatTableColumns.js';
import getTableColumns from './tableColumns';
const { ipcRenderer } = require('electron');
import { customCell, rowClassName, customHeaderCell } from './classAndStyleMethod';

import { useRoute } from 'vue-router';

import specificItem from '@/components/batchDelete/specificItem.vue';
import ReuseGroupPriceDialog from '@/components/ReuseGroupPriceDialog/index.vue';
import qdQuickPricing from '@/components/qdQuickPricing/index.vue';
import bdNameSelect from '@/components/bdNameSelect/index.vue';
import Annotations from '@/components/Annotations/index.vue';
import areaModal from '@/components/areaModal/index.vue';
import { setGlobalLoading } from '@/hooks/publicApiData';
import subMenu from './sub-menu.vue';
import { stableHook } from './stableHook.js';
import { useConstructRcj } from '@/hooks/useConstructRcj';
import { hangZCSB } from './hangZCSB.js';
import DEHangZCSB from './components/DEHangZCSB.vue';
import syncNameToDE from './components/syncNameToDE.vue';
import MaterialMachineIndex from '../materialMachineIndex/index.vue';
import { createModal } from '@/modal/modal';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
const { costPriceFormat } = useDecimalPoint();
const AssociateSubQuotas = defineAsyncComponent(
  () => import('@/components/AssociateSubQuotas/index.vue')
);
const CodeReset = defineAsyncComponent(() => import('./components/CodeReset.vue'));
const PartialSummary = defineAsyncComponent(() => import('@/components/PartialSummary/index.vue'));
let partialSummaryRef = ref();

const route = useRoute();
const typeList = ref([
  {
    name: '材料费',
    value: 2,
  },
  {
    name: '主材费',
    value: 5,
  },
  {
    name: '设备费',
    value: 4,
  },
]);
const { dataSearchPosition } = useReversePosition();
const {
  useCellClickEvent,
  useCellDBLClickEvent,
  cellBeforeEditMethod,
  selectedClassName,
  isSelectedCell,
  resetCellData,
} = useCellClick();

const { checkUnit, showInfo, isComplete } = useCheckBefore();

const components = markRaw(new Map());
components.set(
  'zscy',
  defineAsyncComponent(() => import('../measuresItem/zscyContent.vue'))
);
components.set(
  'zscg',
  defineAsyncComponent(() => import('../measuresItem/zscgContent.vue'))
);
components.set(
  'azfy',
  defineAsyncComponent(() => import('../measuresItem/azfyContent.vue'))
);
components.set(
  'commercial',
  defineAsyncComponent(() => import('../measuresItem/commercialContent.vue'))
);
// 整理子目 清单排序
components.set(
  'qd',
  defineAsyncComponent(() => import('./components/qd.vue'))
);
//整理子目 分部整理
components.set(
  'fb',
  defineAsyncComponent(() => import('./components/fb.vue'))
);

// components.set(
//   'csfy',
//   defineAsyncComponent(() => import('../measuresItem/csfyContent.vue'))
// );
const stableHeight = ref(400);
const stableRef = ref();
let isPriceModel = ref(false);
let showModelType = ref('');
let showPriceTitle = ref('');
// let comMatchModal = ref(false); //组件方案弹框
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
// let currentInfo = ref();
let vexTable = ref();
let frameSelectRef = ref();
const projectStore = projectDetailStore();
const menuOperator = new ItemBillMenuOperator();
let contextmenuList = ref(menuOperator.menus);
let bcContextmenuList = ref(menuOperator.menus);
let szTypeList = ref([]);
let dataType = ref('03');
let isAttrContent = ref(false);
let unitList = ref([]);
let deleteVisible = ref(false);
let splitPercent = ref(0.55);
const emits = defineEmits(['updateMenuList', 'getCurrentInfo']);

//组价部分
const scheduleFile = defineAsyncComponent(() => import('@/components/schedule/schedule.vue'));
const $ipc = cxt.appContext.config.globalProperties.$ipc;
let comMatchModal = ref(false); //组价方案弹框
let percentInfo = ref(); //进度条描述
let percent = ref(0); //进度条百分比
let resetModal = ref(false); //是否确认关闭进度条
let isNoClose = ref(false); //进度条关闭前执行函数
let showSchedule = ref(false);
let reportModel = ref(false); //组价饼图弹框
let startMatchData = ref();

let {
  updateConstructRcj,
  rcjIndexVisible,
  rcjIndexLoading,
  addChildrenRcjData,
  retailAreaRcjReplace,
  batchDeleteRcj,
} = useConstructRcj({
  pageType: 'fbfx',
  $table: vexTable,
  bcRcjCallback: row => {
    rcjVisible.value = true;
    bdCode.value = row.materialCode;
    currentUpdateData.value = row;
  },
  refreshList: (EnterTypes = 'Refresh', posId = '', clearSelect = true) => {
    queryBranchDataById(EnterTypes, posId, clearSelect);
  },
});
let isChangePage = ref([]); //切换页面批注常显隐藏
let {
  updateQdByName,
  dbNameCellClickEvent,
  bdNameTableList,
  bdNamePulldownRef,
  showUnitTooltipType,
  bdNameKeyupEvent,
  onCompositionEnd,
  onCompositionStart,
  isNameOpen,

  editClosedEvent,
  onDragHeight,
  initVirtual,
  renderedList,
  loading: tableLoading,
  addDeInfo,
  EnterType,
  scrollToPosition,
  currentChangeEvent,
  mainMaterialTableData,
  updateFbData,
  queryBranchDataById,
  queryRcjDataByDeId,
  queryFeeFileData,
  queryDjgcFileData,
  saveContent,
  openEditDialog,
  showModelTitle,

  currentInfo,
  isShowModel,
  editContent,
  editKey,
  infoVisible,
  infoText,
  iconType,
  isSureModal,

  ishasRCJList,
  isClearEdit,
  isSortQdCode,
  bdCode,
  rcjVisible,
  currentUpdateData,
  deVisible,
  qdVisible,

  isUpdateFile,
  indexVisible,
  isUpdateQuantities,
  selectUnit,
  showUnitTooltip,
  addCurrentInfo,
  isIndexAddInfo,
  addDataSequenceNbr,
  lockFlag,
  feeFileList,
  djgcFileList,
  tableData,
  originalTableData,
  materialVisible,
  DJGCrefreshFeeFile,
  updateDelTempStatusColl,
  batchDelByTypeOfColl,
  batchDeleteVisible,
  batchDeleteSubItemsVisible,
  subitemIdentification, //整理子目 分部整理（.divisionIdentification） 清单整理标识（.listIdentification）
  subitemBulletFrame,
  codeType,
  radioStyle,
  isOpenLockedStatus,
  batchDataType,
  selectData,
  handleNote,
  handleNoteClick,
  areaStatus,
  areaVisibleType,
  handleMainList,
  handleMainListClick,
  closeAreaModal,
  closeAnnotations,
  getAnnotationsRef,
  cellMouseEnterEvent,
  cellMouseLeaveEvent,
  onFocusNode,
  standardVisible,
  queryRule,
  renderLine,
  isNotCostDe,
  deleteStateFn,
  needAddQDandFB,
  tableKeydown,
  setTableKeydownEnd,
  getTypeText,
  queryProjectConvenientSetColl,
  associateSubQuotasVisible,
  isNextOpen,
  zmList,
  expansionLevel,
  expansionLevelDisabled,
  modelTextareaChange,
  editBlur,
  unitLimit,
  updateFbUpDownMoveStatus,
} = useSubItem({
  operateList,
  frameSelectRef,
  resetCellData,
  checkUnit,
  vexTable: stableRef,
  emits,
  updateConstructRcj,
  pageType: 'fbfx',
  isChangePage,
});
const nameEditClickEvent = ({ row, column }) => {
  console.log('🚀 ~ nameEditClickEvent ~ row, column:', row, column);
};
const {
  hangMenuList,
  hangMenuDisabledHandler,
  DEHangZCSBVisible,
  openDEHangZCSB,
  hangZCSBMenuClick,
  syncToDEVisible,
  isSpecificationEdit,
  addMxqBcRcjData,
} = hangZCSB({
  refreshList: queryBranchDataById,
});

const isContinue = type => {
  if (type === '关闭') {
    isNoClose.value = true;
    resetModal.value = true;
  } else if (type === '暂停') {
    api
      .pauseMerge({
        constructId: projectStore.currentTreeGroupInfo?.constructId,
      })
      .then(res => {});
  } else if (type === '继续') {
    api.startMerge(JSON.parse(JSON.stringify(startMatchData.value))).then(res => {});
  }
};
const closeComMatch = data => {
  //关闭组价方案匹配
  startMatchData.value = data;
  // comMatchModal.value = false;
  // reportModel.value = true; //饼图弹框
  startMatch(data);
};
const visibleChange = (val, row) => {
  if (!val && (row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible)) {
    row.annotationsVisible = row?.noteViewVisible || row?.isShowAnnotations || row?.noteEditVisible;
  }
};
const startMatch = async data => {
  isNoClose.value = true;
  projectStore.SET_START_MATCH(true);
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  //初始化进度条
  percentInfo.value = {
    finish: 0,
    noFinish: 0,
    total: 0,
    dec: '组价方案匹配中，请稍后…',
  };
  percent.value = 0;
  comMatchModal.value = false;
  // setTimeout(() => {
  showSchedule.value = true; //组价进度条开始

  $ipc.on(formData.constructId, (event, arg) => {
    if (arg.percent >= percent.value) {
      percentInfo.value = {
        finish: arg.succeed,
        noFinish: arg.notSuccess,
        total: arg.total,
        dec: arg.percent >= 100 ? '组价方案完成' : '组价方案匹配中，请稍后…',
      };
      percent.value = arg.percent;
    }
    if (arg.percent >= 100) {
      $ipc.removeAllListeners('formData.constructId'); //监听事件移除
      if (isopenReport.value) {
        console.log('ReuseGroupPriceRef.value', ReuseGroupPriceRef.value);
        isopenReport.value = false;
        closeSchedule();
      }
    }
  });
  let res = await api.startMerge(data).then();
  if (res.status === 500 && percent.value === 0) {
    setTimeout(() => {
      percentInfo.value = {
        finish: 0,
        noFinish: 0,
        total: 0,
        dec: '组价方案完成',
      };
      percent.value = 100;
      closeSchedule();
    }, 1000);
  }
};
const closeSchedule = () => {
  isopenReport.value = false;
  setTimeout(() => {
    isNoClose.value = false;
    showSchedule.value = false; //组价进度条关闭
    if (resetModal.value) {
      resetModal.value = false;
    }
    $ipc.removeAllListeners('formData.constructId'); //监听事件移除
    if (projectStore.tabSelectName === '分部分项') {
      projectStore.SET_START_MATCH(false);
      reportModel.value = true; //饼图弹框
      queryBranchDataById('Refresh');
    }
  }, 2000);
};
const recover = async bol => {
  //否，恢复至组价前数据
  //  bol--为true恢复
  let formData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
  };
  let getRes;
  if (bol) {
    getRes = await api.beforeRestoring(formData).then();
  } else {
    getRes = await api.determine(formData).then();
  }
  isNoClose.value = false;
  resetModal.value = false;
  showSchedule.value = false;
  queryBranchDataById('Refresh'); //点击是或否都更新数据
};
//扇形图点击查看功能
const lookView = data => {
  checkList.value = [];
  reportModel.value = false; //饼图弹框
  switch (data.name) {
    case '精准组价':
      checkList.value.push('1');
      break;
    case '近似组价':
      checkList.value.push('2');
      break;
    case '未匹配组价':
      checkList.value.push('0');
      break;
  }
  dataSearchPosition({
    treeId: startMatchData.value.selectedUnitIdList[0],
    tabMenuName: '分部分项',
    type: checkList.value,
  });
};

//项目特征关联
let {
  associationVisible,
  associationRef,
  dblClickHandler,
  projectAttrChange,
  projectAttrFocus,
  popupContainer,
} = useAttrAssociation({ type: 'fbfx' });

// 关联数据双击应用回调
const associationDblClick = (data, row) => {
  console.log('关联数据双击应用回调');
  dblClickHandler({
    data,
    row,
    callback: () => {
      // queryBranchDataById();
      setTimeout(() => {
        quotaInfoRef.value.manualTabChange('groupSchemeTable');
      }, 500);
    },
  });
};

let flag = ref(true); //判断输入的内容是否合法

let menuList = ref([
  {
    type: 0,
    name: '添加分部',
    kind: '01',
    isValid: false,
  },
  {
    type: 1,
    name: '添加子分部',
    kind: '02',
    isValid: false,
  },
  {
    type: 2,
    name: '添加清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '添加子目',
    kind: '04',
    isValid: false,
  },
]);
let bcMenuList = ref([
  {
    type: 2,
    name: '补充清单',
    kind: '03',
    isValid: false,
  },
  {
    type: 3,
    name: '补充子目',
    kind: '04',
    isValid: false,
  },
  {
    type: 3,
    name: '补充人材机',
    kind: '05',
    isValid: false,
  },
]);
const quotaInfoRef = ref();
let deleteList = ref([]);
let page = ref(1);
let limit = ref(300000);
let scrollSwitch = ref(false);
let loading = ref(false);
let deleteLoading = ref(false);
let copyData = ref(null);
const selectState = reactive({
  selectedRowKeys: [],
});
// let showModelTitle = ref('清单名称编辑');
// let isShowModel = ref(false);
// let editContent = ref('');
// let editKey = ref('');
// let infoVisible = ref(false); // 提示信息框是否显示
// let infoText = ref('工程量明细已被调用，是否清空工程量明细？'); // 提示信息框的展示文本
// let iconType = ref(''); // 提示信息框的图标
// let isSureModal = ref(false); // 提示信息框是否为确认提示框
// let isUpdateQuantities = ref(false); // 是否更新工程量明细数据
const pulldownRef = ref(null); // 编码推荐数据ref
const pulldownRefAttr = ref(null); // 项目特征数据ref;
let indexLoading = ref(false); // 索引页面loading

const bcQd = defineAsyncComponent(() => import('./components/bcQd.vue'));
const bcDe = defineAsyncComponent(() => import('./components/bcDe.vue'));
const bcRcj = defineAsyncComponent(() => import('./components/bcRcj.vue'));

let isBatchDelete = ref(false); // 是否批量删除
let standardData = ref([]); // 定额数据下挂的标准换算数据,如若为空,则不展示设置标准换算弹框
// let materialVisible = ref(false); // 是否设置主材市场价弹框
// let mainMaterialTableData = ref([]); // 定额数据下挂的主材数据,如若为空,则不展示设置主材价格弹框
let isUpdate = ref(false); // 修改主材市场价刷新人材机明细数据
const checkList = ref([]); // 组价方案匹配筛选选中值
const initFun = () => {
  projectStore.isOpenIndexModal = {
    open: false,
    tab: null,
  };
  selectState.selectedRowKeys = [];
  projectStore.subItemProjectAutoPosition = {
    queryBranchDataById,
    copyAndPaste,
    posRow,
    selectData: selectData.value,
  };
  isChangePage.value = [];
  initColumns({
    columns: getTableColumns(emits, 'fbfx'),
    pageName: 'fbfx',
  });
  page.value = 1;
  queryFeeFileData();
  querySzType();
  if (!projectStore.isAutoPosition) {
    currentInfo.value = null;
    // 不是自动定位的才调用接口
    queryBranchDataById('other');
  }
};
watch(
  () => [
    projectStore.asideMenuCurrentInfo?.sequenceNbr,
    // projectStore.currentTreeInfo, 解决 由A单位下分部分项 切换到 B单位下，异常调用B单位的分部分项，sequenceNbr 未更新，导致接口报错问题
  ],
  async () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      console.log('分部分项测试入口：watch');
      initFun();
    }
  }
);
// const modal1 = createModal('inventory-and-quota-index', {}, (name, data) => {
//   console.log('getEmit(data.name,data.data)', name, data);
//   if (name === 'currentQdDeInfo' && projectStore.tabSelectName === '分部分项') {
//     currentQdDeInfo(data);
//   }
//   if (
//     name === 'currentInfoReplace' &&
//     projectStore.tabSelectName === '分部分项'
//   ) {
//     currentInfoReplace(data);
//   }
//   if (
//     name === 'update:indexVisible' &&
//     projectStore.tabSelectName === '分部分项'
//   )
//     indexVisible.value = false;
// });
watch(
  () => indexVisible.value,
  async val => {
    // 调用窗口方法
    // if (indexVisible.value) {
    //   modal1.openModal({
    //     indexVisible: toRaw(indexVisible.value),
    //     dataType: toRaw(dataType.value),
    //     indexLoading: toRaw(indexLoading.value),
    //     originInfo: toRaw(currentInfo.value),
    //   });
    // }
  }
);
const timeCLose = (closeEditor, { row, column }, modelValue, name, late = 200) => {
  setTimeout(() => {
    !isNameOpen.value ? editClosedEvent({ row, column }, modelValue, name) : '';
    isNameOpen.value = false;
  }, 200);
  setTimeout(() => {
    closeEditor();
  }, late);
};
watch(
  () => lockFlag.value,
  () => {
    operateList.value.find(item => item.name === 'code-reset').disabled = lockFlag.value;
  }
);

watch(
  () => projectStore.standardGroupOpenInfo.isOpen,
  () => {
    let reuse = operateList.value.find(item => item.name === 'reuse-group-price').options[2]; //标准组价子窗口复用组价提取已有清单隐藏
    if (projectStore.standardGroupOpenInfo.isOpen) {
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-ruotixing',
        infoText: '提取成功！',
        descText: projectStore.standardGroupOpenInfo.modalTip,
        confirm: () => {
          infoMode.hide();
        },
      });
      reuse.isValid = false;
    } else {
      reuse.isValid = true;
    }
  }
);
watch(
  () => projectStore.positionId,
  () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.positionId &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      currentInfo.value = { sequenceNbr: projectStore.positionId };
      queryBranchDataById('other', projectStore.positionId);
    }
  }
);

watch(
  () => projectStore.combinedSearchList,
  () => {
    if (
      projectStore.tabSelectName === '分部分项' &&
      projectStore.combinedSearchList &&
      projectStore.currentTreeInfo?.levelType === 3
    ) {
      if (!projectStore.combinedVisible) projectStore.SET_COMBINED_VISIBLE(true);
      nextTick(() => {
        filterData(projectStore.combinedSearchList);
      });
    }
  }
);
watch(
  () => tableLoading.value,
  () => {
    setGlobalLoading(tableLoading.value, '加载中，请稍后');
  }
);

watch(
  () => currentInfo.value,
  newVal => {
    deleteStateFn();
    colorStateFn();
    if (newVal) {
      // modal1.postMsgToChild({
      //   indexVisible: toRaw(indexVisible.value),
      //   dataType: toRaw(dataType.value),
      //   indexLoading: toRaw(indexLoading.value),
      //   originInfo: toRaw(currentInfo.value),
      // });
      // if (
      //   selectState.selectedRowKeys.includes(newVal.sequenceNbr) &&
      //   selectState.selectedRowKeys.length > 0
      // ) {
      //   selectState.selectedRowKeys = Array.from(
      //     new Set([...selectState.selectedRowKeys, ...[newVal.sequenceNbr]]),
      //   );
      // } else {
      //   selectState.selectedRowKeys = [newVal.sequenceNbr];
      // }
      if (!selectState.selectedRowKeys.length) {
        selectState.selectedRowKeys = [newVal.sequenceNbr];
      }
      updateFbUpDownMoveStatus();
    }
  }
);
onMounted(async () => {
  console.log('分部分项测试入口：onMounted');

  if (projectStore.asideMenuCurrentInfo?.sequenceNbr && projectStore.tabSelectName === '分部分项') {
    initFun();
  }
  // window.addEventListener('keydown', copyAndPaste);
});

let lockBtnStatus = ref(false);
let defaultValue1 = ref(''); // 清单定额索引默认选中值

onActivated(() => {
  console.log('分部分项测试入口：onActivated');
  lockBtnStatus.value = false;
  console.log('handleCopyEvent');
  bus.off('handleCopyEvent');
  bus.on('handleCopyEvent', ({ event, name }) => {
    if (name === 'subItemProject') copyAndPaste(event);
  });
  bus.off('moveDeData');
  bus.on('moveDeData', ({ state, type }) => {
    moveDeData({ state, type });
  });
  bus.off('asideMenuRightClickHandler');
  bus.on('asideMenuRightClickHandler', async ({ name, data, menuKey }) => {
    if (name === 'subItemProject') {
      console.log(name, data, menuKey);
      if (menuKey !== 0) {
        // 显示分部
        await openBranchHandler(data);
        setTimeout(() => {
          data.expandOrNot && data.expandOrNot();
        }, 100);
      } else {
        // 删除分部
        branchIndexDeleteHandler(data);
      }
    }
  });

  insetBus(bus, projectStore.componentId, 'subItemProject', async data => {
    console.log('🚀 ~ onActivated ~ data:', data, bus);
    if (
      !['insert-subItem', 'supplement', 'reuse-group-price'].includes(data.name) &&
      !(await showInfo())
    )
      return;
    if (data.name === 'insert-subItem') {
      if (!data.activeKind) {
        contextMenu();
      } else {
        addData(data.activeKind);
      }
    }
    if (data.name === 'supplement') {
      if (!data.activeKind) {
        bcContextMenu();
      } else {
        bcData(data);
      }
    }
    if (data.name === 'select-color') {
      if (!data.activeKind) {
      } else {
        updateDataColorColl(data);
      }
    }
    // 展开到
    if (data.name === 'expandLevel') {
      console.log(data);
      await expansionLevelDisabled(data); // 置灰展开到的层级
      if (data.activeKind) {
        expansionLevel(data.activeKind);
      }
    }
    // 整理子目
    if (data.name === 'organize-subitems') {
      if (data.activeKind) {
        subitemBulletFrame(data);
      }
    }
    operateList.value.find(item => item.name === 'lock-subItem').label = lockFlag.value
      ? '整体解锁'
      : '整体锁定';
    operateList.value.find(item => item.name === 'code-reset').disabled = lockFlag.value;
    if (data.name === 'delete-subItem') deleteType(null);
    if (data.name === 'delete-blank-row') delBlankRowFun(0);
    if (data.name === 'code-reset') {
      openCodeResetDialog();
    }
    if (data.name === 'vertical-transport') showModel('zscy');
    if (data.name === 'superelevation') showModel('zscg');
    if (data.name === 'installation-costs') showModel('azfy');
    if (data.name === 'calculate-commercial') showModel('commercial');
    if (data.name === 'lock-subItem') allLock();
    if (data.name === 'component-matching') checkOnline(data);
    if (data.name === 'reuse-group-price') openReuseGroupPrice(data);
    if (data.name === 'qd-group-price') openQdQuickPricing(data);
    if (data.name === 'standard-group-price') openStandardGroupPrice(data);
    if (data.name === 'qd-library' || data.name === 'de-library') {
      defaultValue1.value = data.name === 'qd-library' ? '03' : '04';
      indexVisible.value = true;
      dataType.value = currentInfo.value?.kind;
    }
    if (data.name === 'partial-summary') {
      partialSummaryRef.value?.open(tableData.value[0]?.deRowId);
    }
  });
});
onDeactivated(() => {
  lockBtnStatus.value = true;
  qdQuickPricingRef.value?.cancel(false);
});

const openBranchHandler = async data => {
  // 展开分部逻辑
  console.log('openBranchHandler', data);
  await expansionLevel(data.menuKey);
};
const branchIndexDeleteHandler = async data => {
  let postRow = tableData.value.find(i => i.sequenceNbr == data.sequenceNbr);
  // 这块判断处理，如果左边tree，展开了，但是右边分部分项的编辑区的数据没有展开（未展开没数据），导致和右边编辑区比对时，找不到数据；因此如果在找不到情况下，
  // 直接将左边tree要删的数据，赋值到当前选中的数据中去
  if (postRow) {
    currentInfo.value = postRow;
  } else {
    currentInfo.value = data.treeNode.dataRef;
  }
  console.log('handleCopyEvent', data);
  // vexTable.value?.setCurrentRow(postRow);
  // vexTable.value?.scrollToRow(postRow);
  deleteType();
};

let isopenReport = ref(false);
const checkOnline = async data => {
  if (Object.prototype.hasOwnProperty.call(data, 'activeKind')) {
    if (data.activeKind === '01') {
      //点击组件方案匹配
      comMatchModal.value = true;
      isopenReport.value = true;
    } else if (data.activeKind === '02') {
      //点击组件筛选
      if (!projectStore.combinedVisible) projectStore.SET_COMBINED_VISIBLE(true);
    }
  } else {
    const isOnline = checkisOnline(true);
    isOnline
      ? data.options.forEach(item => (item.isValid = true))
      : data.options.forEach(item => (item.isValid = false));
  }
};
onBeforeUnmount(() => {
  // window.removeEventListener('keydown', copyAndPaste);
});

let {
  sTableState,
  setInputRef,
  openEditor,
  setCloseEditor,
  clickOutside,
  cellMouseup,
  rowSelection,
  customRow,
  pasteRowVisible,
  cellKeydown,
  copyAndPaste,
  cutFun,
  copyFun,
  pasteFun,
  isAllSelectedZCSB,
  mousedownHandle,
  cellClickEvent: sTableCellClickEvent,
  nextClickEditHandler,
} = stableHook(
  {
    selectState: selectState,
    stableRef: stableRef,
    currentInfo: currentInfo,
    tableData: tableData,
    originalData: tableData,
    copyData: copyData,
  },
  'fbfx',
  msg => {
    if (msg === 'refresh') queryBranchDataById();
    if (msg === 'projectAttr-open') {
      isAttrContent.value = true;
      setTimeout(() => {
        isAttrContent.value = false;
      }, 100);
    }
    if (msg === 'delete') {
      deleteType();
    }
  }
);
//点击编辑区之外的位置
onClickOutside(stableRef, clickOutside);
// /**
//  *
//  * @param {*} event
//  * @param {*} isHandCopy 是否手动执行复制操作
//  */
// const copyAndPaste = (event, isHandCopy = false) => {
//   if (['input', 'textarea'].includes(event.target.nodeName.toLowerCase()))
//     return;
//   console.log('复制', event.target.nodeName.toLowerCase());
//   // 如果选中数据为空，情景1，刚开始进入页面，2点击了input,然后点击空白处
//   if (!selectData.value || !selectData.value?.data?.length) {
//     frameSelectRef.value?.isBranchCopy([currentInfo.value?.sequenceNbr]);
//   }
//   if (isHandCopy) {
//     copyFun();
//   }

//   if (event.ctrlKey && event.code === 'KeyC') {
//     copyFun();
//   }
//   if (event.ctrlKey && event.code === 'KeyV') {
//     if (!vexTable.value.getSelectedCell()) return; //vexTable.value.getSelectedCell()如果当前表格不是选中，就不进行ctr+v
//     pasteFun();
//   }
// };
let codeResetVisible = ref(false);
const openCodeResetDialog = () => {
  codeResetVisible.value = true;
};
// let refreshType = ref(2);
// const batchRefresh = () => {
//   let apiData = {
//     constructId: projectStore.currentTreeGroupInfo?.constructId,
//     singleId: projectStore.currentTreeGroupInfo?.singleId,
//     unitId: projectStore.currentTreeInfo?.id,
//     type: refreshType.value,
//   };
//   console.log('编码冲刷参数', apiData);
//   api.batchRefresh(apiData).then(res => {
//     if (res.status === 200 && res.result) {
//       page.value = 1;
//       message.success('项目编码重刷成功');
//       queryBranchDataById();
//       codeResetVisible.value = false;
//     }
//   });
// };
// const copyFun = () => {
//   if (!selectData.value) {
//     message.error('暂无选中数据');
//   } else {
//     // copyTableToClipboard(vexTable.value.$el)
//     if (selectData.value.isCopy) {
//       copyData.value = selectData.value;
//       let apiData = {
//         constructId: projectStore.currentTreeGroupInfo?.constructId,
//         singleId: projectStore.currentTreeGroupInfo?.singleId,
//         unitId: projectStore.currentTreeInfo?.id,
//         sequenceNbrs: selectData.value.data.map(i => i.sequenceNbr),
//       };
//       api.copyQdDeFbData(apiData).then(res => {
//         console.log('复制数据', apiData, projectStore);
//         if (res.status === 200 && res.result) {
//           message.success('已复制');
//         }
//       });
//     } else {
//       message.error(selectData.value.msg);
//     }
//   }
// };
// const pasteFun = async () => {
//   // let clipboardText;
//   // const clipPromise = navigator.clipboard.readText();
//   // await clipPromise.then(function (clipText) {
//   //   //粘贴板粘贴的数据
//   //   clipboardText = clipText;
//   //   if (!copyData.value && clipboardText) {
//   //     copyData.value = clipboardText;
//   //   }
//   // });
//   // console.log('clipboardText', clipboardText);
//   if (!copyData.value) {
//     message.error('暂无复制数据');
//   } else {
//     if (
//       projectStore.standardGroupOpenInfo.isOpen &&
//       copyData.value.data.find(i => i.kind === '03')
//     ) {
//       message.error('标准组价不可粘贴包含清单行数据');
//       return;
//     }
//     if (!frameSelectRef.value.getRowCurrent()) {
//       return message.error('请选中需要粘贴行！');
//     } else {
//       let row = frameSelectRef.value.getRowCurrent();
//       try {
//         await frameSelectRef.value.frameSelectJs.isPasteBranch(
//           row,
//           copyData.value
//         );
//         console.log('粘贴数据到此页面：', copyData.value);
//         batchPasteQdDeData();
//         // frameSelectRef.value.clearSelect();
//         // copyData.value = null;
//         // selectData.value = null;
//       } catch (error) {
//         message.error(error);
//       }
//     }
//   }
// };

const scrollTop = y => {
  console.log('scrollTop', y);
};
const scrollTo = (x, y) => {
  vexTable.value.scrollTo(0, vexTable.value.getScroll().scrollTop + y);
};
const getSelectData = val => {
  selectData.value = val;
};

// 定位方法
const posRow = sequenceNbr => {
  currentInfo.value = { sequenceNbr };
  queryBranchDataById('other', sequenceNbr);
};

const addType = () => {
  console.log('插入类型');
};

const bcHandle = async () => {
  if (!(await showInfo())) return;
};

const queryExistsGlDeColl = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    type: 'fbfx',
    idList: JSON.parse(JSON.stringify(selectState.selectedRowKeys)),
  };
  console.log('关联子定额删除参数', apiData);
  api.queryExistsGlDeColl(apiData).then(res => {
    console.log('批量删除数据', res);
    if (res.status === 200) {
      if (res.result) {
        infoMode.show({
          iconType: 'icon-qiangtixing',
          infoText: '删除主子目时将删除对应关联子目，是否继续?',
          descText: projectStore.standardGroupOpenInfo.modalTip,
          confirm: () => {
            if (selectState.selectedRowKeys.length > 1) {
              let index = tableData.value.findIndex(
                x => x.sequenceNbr === selectState.selectedRowKeys[0]
              );
              page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
              delBatchData(index);
            } else {
              delSingleFbData();
            }
            infoMode.hide();
          },
          close: () => {
            infoMode.hide();
          },
        });
      } else {
        deleteInfo();
      }
    }
  });
};
//删除空白行
const delBlankRow = ref({
  visible: false,
  val: 1,
});
const initDelBlankRow = () => {
  delBlankRow.value.visible = false;
  delBlankRow.value.val = 1;
};
const delBlankRowFun = type => {
  console.log('删除空白行');
  if (!type) delBlankRow.value.visible = true;
  if (type) {
    console.log('delBlankRowFun', type, delBlankRow.value.val);
    let apiData = {
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      type: 0,
    };
    if (delBlankRow.value.val === 1) {
      apiData.singleId = projectStore.currentTreeGroupInfo?.singleId;
      apiData.unitId = projectStore.currentTreeInfo?.id;
    }
    console.log('deleteEmptyDate', apiData);
    api
      .deleteEmptyDate(apiData)
      .then(res => {
        if (res.status === 200) {
          message.success('删除成功');
          emits('updateMenuList');
          queryBranchDataById();
        } else {
          message.error(res.message);
        }
        initDelBlankRow();
      })
      .catch(err => {
        console.log('----------分部分项删除数据', err);
        message.error('删除失败');
        initDelBlankRow();
      });
  }
};
// 删除数据
const deleteType = async () => {
  let isDetete = operateList.value.find(item => item.name === 'delete-subItem');
  // 删除按钮置灰不可删除
  if (isDetete.disabled) return;
  isBatchDelete.value = false;
  if (!(await showInfo())) {
    return;
  }
  if (currentInfo.value.kind === '0') {
    message.warning('该行不可删除');
    return;
  }
  deleteList.value = [];
  let tempList = [];
  tableData.value.forEach(item => {
    if (selectState.selectedRowKeys.includes(item.sequenceNbr)) {
      tempList.push(item);
    }
  });
  let status = tempList.some(x => x.kind === '04');
  if (status) {
    queryExistsGlDeColl();
    return;
  }
  deleteInfo();
};
let isShowDelNow = ref(false); //删除分部还需要判断它有子级分部的时候删除文字展示删除当前行
const deleteInfo = () => {
  isShowDelNow.value = false;
  if (selectState.selectedRowKeys.length > 1) {
    isBatchDelete.value = true;
  } else {
    currentInfo.value.optionMenu.forEach(item => {
      if (item === 4 || item === 5) {
        deleteList.value.push(item);
      }
    });
    if (['01', '02'].includes(currentInfo.value.kind) && isHasSameLevel()) {
      //当前选中删除行为分部-它有同级分部
      isShowDelNow.value = true;
    }
    console.log('选中行有同级分部', isHasSameLevel());
  }
  if (deleteList.value.length > 0 || isBatchDelete.value) {
    deleteVisible.value = true;
  }
};
const isHasSameLevel = () => {
  let sameLevel = tableData.value.find(
    a =>
      a.parentId === currentInfo.value.parentId &&
      a.sequenceNbr !== currentInfo.value.sequenceNbr &&
      ['02', '01'].includes(a.kind)
  );
  return sameLevel ? true : false;
};
const cancel = () => {
  deleteVisible.value = false;
};

const addData = async kind => {
  if (!(await showInfo())) return;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    pointLine: {
      kind: currentInfo.value.kind,
      sequenceNbr: currentInfo.value.sequenceNbr,
      parentId: currentInfo.value.parentId,
      displayStatu: currentInfo.value.displayStatu,
      displaySign: currentInfo.value.displaySign,
    },
    newLine: {
      kind: kind,
    },
  };
  api.addQdDeFbData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      if (kind === '01' || kind === '02') {
        emits('updateMenuList');
      }
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      message.success('插入成功');
      queryBranchDataById();
      queryDjgcFileData();
    }
  });
};

/**
 * 点击index事件 为什么从上面单独拿下来，因为收起分部，然后这时候多次点击，没有触发currentChangeEvent事件。所以拿不到当前行子级数据了就是空数据了
 * @param {*} row
 */
const clickIndex = row => {
  const $table = vexTable.value;
  // 判断单元格值是否被修改
  if ($table.isUpdateByRow(currentInfo.value)) return;
  currentInfo.value = row;
  projectStore.SET_SUB_CURRENT_INFO(row);
  nextTick(() => {
    if (row.kind === '04') return;
    // 等选中的样式更新完，
    queryAllDataByBranchId();
  });
};

const isFlag = row => {
  //判断输入工程量表达式是否合法
  let input = row.quantityExpression.replace(row.originalQuantityExpression, '');
  let flag = true;
  let inputList = input.match(/[A-Za-z0-9_]+(\.\d+)?/g);
  const reg = /[^\d.]/g;
  inputList &&
    inputList.map(item => {
      if (reg.test(item)) {
        flag = false;
        return;
      }
    });
  return flag;
};

const saveCustomInput = (newValue, row, name, index) => {
  if (newValue) {
    row[name] = newValue;
  }
};

// 弹框提示点击确定按钮事件
const updateCurrentInfo = () => {
  updateFbData(currentInfo.value, 'quantityExpression');
};

const closeDialog = () => {
  queryProjectConvenientSetColl();
  standardVisible.value = false;
};

// 工程量明细更改后刷新当前行数据
const refreshCurrentInfo = (refreshFeeFile = false) => {
  console.log(
    '🚀 ~ r工程量明细更改后刷新当前行数据eeFile:',
    projectStore.tabSelectName,
    refreshFeeFile,
    currentInfo.value
  );
  if (projectStore.tabSelectName !== '分部分项') return;
  addDataSequenceNbr.value = addCurrentInfo.value
    ? addCurrentInfo.value.sequenceNbr
    : currentInfo.value.sequenceNbr;
  DJGCrefreshFeeFile.value = refreshFeeFile ? true : false;
  queryBranchDataById();
};

/**
 * 设置主材市场价弹框操作更新
 * @param type 1 关闭弹框, 2 刷新数据
 */
const setUpdate = type => {
  materialVisible.value = false;
  if (type === 2) {
    isUpdate.value = true;
    setTimeout(() => {
      isUpdate.value = false;
    }, 100);
    addDataSequenceNbr.value = addCurrentInfo.value
      ? addCurrentInfo.value.sequenceNbr
      : currentInfo.value.sequenceNbr;
    queryBranchDataById();
  }
  if (projectStore.globalSettingInfo.standardConversionShowFlag) {
    queryRule();
  } else if (projectStore.globalSettingInfo.crdezsdeglzmtk) {
    // 解决标准换算关闭，关联子目不弹出
    queryProjectConvenientSetColl();
  }
};

/**
 * @param {*} v
 *
 * type:'groupSchemeTable', 业务
    filed:'projectAttr', 字段
    value: row.projectAttr 值
    isRefresh: 是否刷新
 */
const onDbClickFile = v => {
  updateFbData(
    {
      ...currentInfo.value,
      projectAttr: v.value,
    },
    'projectAttr'
  );
};

const changeStatus = row => {
  let index = tableData.value.findIndex(x => x.sequenceNbr === row.sequenceNbr);
  page.value = Math.ceil((index + 1) / limit.value);
  if (row.displaySign === 1) {
    row.displaySign = 2;
    closeTree(row);
  } else if (row.displaySign === 2) {
    row.displaySign = 1;
    openTree(row);
  }
  // updateFbData(row, 'seq');
};

const openTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.openTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};
const closeTree = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(row)),
  };
  api.closeTree(apiData).then(res => {
    if (res.status === 200 && res.result) {
      queryBranchDataById();
    }
  });
};

const toggleMethod = ({ expanded, row }) => {
  if (expanded) {
  } else {
  }
  // row.closeFlag = expanded
  // updateFbData(row)
  return true;
};

const cellDBLClickEvent = async ({ row, column }, db = '0') => {
  if (!sTableState.prevDbTime && db === '0') return;
  if (['bdCode', 'projectAttr'].includes(column.field) && !(await showInfo())) return;
  // if (projectStore.standardGroupOpenInfo.isOpen) return;
  const time = new Date().getTime();
  if (column.field === 'bdCode' && db === '1') {
    if ([94, 95].includes(row.kind)) {
      rcjIndexVisible.value = true;
    } else {
      defaultValue1.value = '';
      indexVisible.value = true;
      dataType.value = row.kind;
    }
  } else if (column.field === 'projectAttr') {
    isAttrContent.value = true;
    setTimeout(() => {
      isAttrContent.value = false;
    }, 100);
  }
  currentInfo.value = row;
};

// 表格单击事件
const tableCellClickEvent = ({ record, column }) => {
  console.log('record, column111', record, column);
  if ((record.isLocked && !['bdCode'].includes(column.field)) || record.tempDeleteFlag)
    return false;
  if (!nextClickEditHandler(column, record)) {
    return false;
  }
  return true;
};

const successHandler = addList => {
  addDataSequenceNbr.value = addList[0]?.data?.sequenceNbr;
  queryBranchDataById();
};

const quotasCancel = () => {
  associateSubQuotasVisible.value = false;
  if (zmList.value.length) {
    isNextOpen.value = true;
  }
};

let checkBatchOption = ref(false); // 清单定额索引是否有在批量操作中

const currentQdDeInfo = row => {
  // let apiData = {
  //   unit: row.unit,
  //   sequenceNbr: row.sequenceNbr,
  // };
  fillFromIndexPage(row);
  // console.log('1111111111', apiData.formData);
  // addData(row.deName ? '04' : '03', apiData);
};
const fillFromIndexPage = row => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    kind: row.kind,
    indexId: row.sequenceNbr,
    unit: row.unit,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    rcjFlag: row.rcjFlag,
    fbfxOrCsxm: 'fbfx',
    libraryCode: row.libraryCode,
  };

  let apiName = 'fillFromIndexPage';

  if (row.deArray?.length) {
    // 清单指引点击插入子目保存
    apiData.deArray = row.deArray;
    apiName = 'saveDeArray';
  }

  if (row.baseListModel?.sequenceNbr) {
    // 清单指引点击插入清单
    apiData.baseListModel = row.baseListModel;
    apiName = 'saveQdAndDeArray';
  }

  indexLoading.value = true;

  console.log('发送数据', apiData);

  api[apiName](apiData)
    .then(res => {
      console.log('插入成功！', res);
      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        // if (
        //   currentInfo.value?.kind !== '03' &&
        //   currentInfo.value?.kind !== '04' &&
        //   row.kind === '03'
        // ) {
        // } else {
        isIndexAddInfo.value = true;
        // }

        if (apiName === 'saveDeArray') {
          // 插入子目
          if (res.result.length) {
            // 处理批量插入定额只出现一次，应按顺序弹出主材、标准换算、子目定额关联弹框
            zmList.value = res.result;
            addDeInfo.value = res.result[0].data;
            addDataSequenceNbr.value = res.result[0].sequenceNbr;
            zmList.value.splice(0, 1);
            queryRcjDataByDeId();
          }
          // res.result.forEach((item, index) => {
          //   if (index === 0) {
          //     addDataSequenceNbr.value = item.data.sequenceNbr;
          //     addDeInfo.value = item.data;
          //     queryRcjDataByDeId();
          //   }
          // });
        } else if (apiName === 'saveQdAndDeArray') {
          addDataSequenceNbr.value = res.result?.saveQdResult?.data?.sequenceNbr;
          const saveDeArrayResult = res.result?.saveDeArrayResult;
          if (saveDeArrayResult?.length) {
            // 清单指引中插入清单携带定额时，执行插入定额之后的弹框检测操作
            zmList.value = saveDeArrayResult;
            addDeInfo.value = saveDeArrayResult[0].data;
            zmList.value.splice(0, 1);
            queryRcjDataByDeId();
          }
        } else {
          addDataSequenceNbr.value = res.result.data.sequenceNbr;
          currentInfo.value = res.result.data;
          page.value = Math.ceil((res.result.index + 1) / limit.value);
          // if (row.kind === '01' || row.kind === '02') {
          //   emits('updateMenuList');
          // }
          if (row.kind === '04' && row.rcjFlag === 0) {
            addDeInfo.value = res.result.data;
            queryRcjDataByDeId();
            // standardVisible.value = true;
          }
        }
        if (!row.isBatchInsert) {
          message.success('插入成功');
          queryBranchDataById();
        } else {
          // 更改批量操作标识，进入下一个插入
          checkBatchOption.value = true;
        }
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};
// 替换功能
const currentInfoReplace = row => {
  console.log('🚀 ~ currentInfoReplace ~ row:', row);
  indexLoading.value = true;
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    selectId: row.sequenceNbr,
    replaceId: currentInfo.value.sequenceNbr,
    type: 1,
    conversionCoefficient: row.conversionCoefficient,
    kind: row.kind,
    unit: row.unit,
    fbfxOrCsxm: 'fbfx',
    libraryCode: row.libraryCode,
    rootLineId: route.query.constructSequenceNbr,
  };

  let apiName = 'replaceItemBillData';
  if (row?.qdzyReplace) {
    apiName = 'replaceQdAndSaveDeArray';
    apiData.pointLine = JSON.parse(JSON.stringify(currentInfo.value));
    apiData.deArray = row.deArray;
    apiData.baseListModel = row.baseListModel;
  }
  console.log('🚀 ~ currentInfoReplace ~ apiData:', apiData);

  api[apiName](apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        indexLoading.value = false;
        if (apiData.deArray?.length) {
          queryRcjDataByDeId();
        } else if (row.kind === '04' && row.rcjFlag === 0) {
          addDeInfo.value = res.result;
          queryRcjDataByDeId();
        }
        queryBranchDataById();
        addDataSequenceNbr.value = res.result.sequenceNbr;
      } else {
        indexLoading.value = false;
      }
    })
    .finally(() => {
      indexLoading.value = false;
    });
};

const contextMenu = () => {
  let tempList = xeUtils.clone(menuList.value, true);
  tempList.forEach(item => {
    currentInfo.value.optionMenu.forEach(child => {
      if (child === item.type) {
        item.isValid = true;
      }
    });
  });
  contextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'insert-subItem').options = tempList;
};

const bcContextMenu = () => {
  let tempList = xeUtils.clone(bcMenuList.value, true);
  tempList.forEach(item => {
    item.isValid = false;
    currentInfo.value.optionMenu.forEach(child => {
      if (
        child === item.type ||
        ([94, 95].includes(currentInfo.value.kind) && item.name === '补充人材机')
      ) {
        // || ([94, 95].includes(currentInfo.value.kind) && item.name === '补充人材机')
        item.isValid = true;
      }
    });
  });
  bcContextmenuList.value = tempList;
  operateList.value.find(item => item.name === 'supplement').options = tempList;
};

const bcData = async item => {
  if (!(await showInfo())) return;
  bdCode.value = '';
  if (item.activeKind === '03') {
    qdVisible.value = true;
  } else if (item.activeKind === '04') {
    deVisible.value = true;
  } else {
    rcjVisible.value = true;
  }
};

const querySzType = () => {
  api.querySzType({ constructId: route.query.constructSequenceNbr }).then(res => {
    if (res.status === 200 && res.result) {
      szTypeList.value = res.result;
    }
  });
};

const queryUnit = () => {
  api.queryUnit().then(res => {
    if (res.status === 200 && res.result) {
      unitList.value = res.result;
    }
  });
};

const delFbData = type => {
  if (deleteLoading.value) return;
  deleteLoading.value = true;
  if (isAllSelectedZCSB()) {
    batchDeleteRcj(selectState.selectedRowKeys, () => {
      deleteVisible.value = false;
      deleteLoading.value = false;
    });
    return;
  }
  console.log(currentInfo.value);
  if (!type && currentInfo.value && ['01', '02'].includes(currentInfo.value?.kind)) {
    //只删除当前分部行  下有清单且有同级分部，不可删除
    let index = tableData.value.findIndex(x => x.sequenceNbr === currentInfo.value.sequenceNbr);
    let child = tableData.value[index + 1];
    let sameLevel = tableData.value.find(
      a =>
        a.parentId === currentInfo.value.parentId &&
        a.sequenceNbr !== currentInfo.value.sequenceNbr &&
        ['02', '01'].includes(a.kind)
    );
    if (sameLevel && child?.kind === '03') {
      deleteVisible.value = false;
      deleteLoading.value = false;
      infoMode.show({
        isSureModal: true,
        iconType: 'icon-querenshanchu',
        infoText: '删除分部会导致分部和清单同级，不允许该操作！',
        confirm: async () => {
          infoMode.hide();
        },
      });
      return;
    }
    console.log(child, index, sameLevel);
  }
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(x => x.sequenceNbr === selectState.selectedRowKeys[0]);
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData(index);
    return;
  }
  delSingleFbData(type);
};
const getPrevOrNextDataByIndex = index => {
  return tableData.value[index - 1];
};
const delSingleFbData = type => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    isBlock: type,
    sequenceNbr: currentInfo.value.sequenceNbr,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  let index = tableData.value.findIndex(x => x.sequenceNbr === currentInfo.value.sequenceNbr);
  if (isBatchDelete.value) {
    let index = tableData.value.findIndex(
      x => x.sequenceNbr === selectData.value.data[0].sequenceNbr
    );
    page.value = index === 1 ? 1 : Math.ceil((index - 1) / limit.value);
    delBatchData(index);
    return;
  }
  console.log('删除data', apiData);
  api
    .delFbData(apiData)
    .then(res => {
      if (res.status === 200 && res.result) {
        deleteVisible.value = false;
        deleteLoading.value = false;
        message.success('删除成功');
        page.value = Math.ceil((index + 1) / limit.value);
        if (currentInfo.value.kind === '01' || currentInfo.value.kind === '02') {
          emits('updateMenuList');
        }
        queryBranchDataById();
      } else {
        message.error(res.message);
        deleteVisible.value = false;
        deleteLoading.value = false;
        selectData.value.data = [];
      }
    })
    .catch(err => {
      console.log('----------分部分项删除数据', err);
      message.error('删除失败');
      selectData.value.data = [];
      deleteLoading.value = false;
      deleteVisible.value = false;
    });
};
const menuConfig = reactive({
  className: 'my-menus-subItem',
  body: {
    options: [
      [
        {
          code: 'add',
          name: '插入',
          children: [
            {
              code: 0,
              name: '添加分部',
              kind: '01',
              visible: true,
              disabled: true,
            },
            {
              code: 1,
              name: '添加子分部',
              kind: '02',
              visible: true,
              disabled: true,
            },
            {
              code: 2,
              name: '添加清单',
              kind: '03',
              visible: true,
              disabled: true,
            },
            {
              code: 3,
              name: '添加子目',
              kind: '04',
              visible: true,
              disabled: true,
            },
          ],
        },
        {
          code: 'copy',
          name: '复制',
          visible: true,
          disabled: false,
        },
        {
          code: 'paste',
          name: '粘贴',
          visible: true,
          disabled: false,
        },
        {
          code: 'delete',
          name: '删除',
          visible: true,
          disabled: true,
        },
        {
          code: 'lock',
          name: '清单锁定',
          visible: true,
          disabled: false,
        },
        {
          code: 'tempDelete',
          name: '临时删除',
          visible: true,
          disabled: false,
        },
        {
          code: 'MainList',
          name: '主要清单',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'set-list',
              name: '设置主要清单',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-current',
              name: '取消当前行',
              type: 2,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-all',
              name: '取消所有清单',
              type: 3,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'noteList',
          name: '批注',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'add-note',
              name: '插入批注',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'edit-note',
              name: '编辑批注',
              type: 2,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-note',
              name: '删除批注',
              type: 3,
              visible: true,
              disabled: false,
            },
            {
              code: 'show-note',
              name: '显示批注',
              type: 4,
              visible: true,
              disabled: false,
            },
            {
              code: 'hide-note',
              name: '隐藏批注',
              type: 5,
              visible: true,
              disabled: false,
            },
            {
              code: 'del-all-note',
              name: '删除所有批注',
              type: 6,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'batchDelete',
          name: '批量删除',
          visible: true,
          disabled: false,
          children: [
            {
              code: 'batchDelete-child1',
              name: '批量删除所有临时删除项',
              type: 1,
              visible: true,
              disabled: false,
            },
            {
              code: 'batchDelete-child2',
              name: '批量删除所有工程量为0项',
              type: 2,
              visible: true,
              disabled: false,
            },
          ],
        },
        {
          code: 'pageColumnSetting',
          name: '页面显示列设置',
          visible: true,
          disabled: false,
        },
      ],
    ],
  },
  visibleMethod({ options, column, columnIndex, row, rowIndex }) {
    if (!row) return;
    // frameSelectRef.value?.clearSelect();
    currentInfo.value = row;
    projectStore.SET_SUB_CURRENT_INFO(row);
    vexTable.value.setCurrentRow(row);
    deleteStateFn();
    let index = selectData.value?.data.findIndex(x => x.sequenceNbr === row.sequenceNbr);
    if (index === -1 && selectData.value?.data.length > 1) {
      selectData.value?.data.push(row);
    }
    options.forEach(list => {
      list.forEach(async (item, index) => {
        if (!copyData.value && item.code === 'paste') {
          item.disabled = true;
        }
        if (copyData.value && item.code === 'paste') {
          item.disabled = false;
          //粘贴代码中已经有此逻辑判断-注释
          try {
            await frameSelectRef.value.frameSelectJs.isPasteBranch(row, copyData.value);
            item.disabled = false;
          } catch (error) {
            item.disabled = true;
          }
        }
        if (item.code === 'delete') {
          if (
            (currentInfo.value.optionMenu.includes(4) ||
              currentInfo.value.optionMenu.includes(5)) &&
            !currentInfo.value.isLocked
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
        }
        if (item.code === 'lock') {
          if (projectStore.standardGroupOpenInfo.isOpen) {
            item.disabled = true;
          } else {
            if (currentInfo.value.kind === '03') {
              item.disabled = false;
            } else {
              item.disabled = true;
            }
            if (currentInfo.value.isLocked) {
              item.name = '清单解锁';
            } else {
              item.name = '清单锁定';
            }
          }
        } else if (item.code === 'tempDelete') {
          let parentInfo = renderedList.value.filter(
            x => x.sequenceNbr === currentInfo.value.parentId
          )[0];
          if (
            (currentInfo.value.kind === '03' && !currentInfo.value.isLocked) ||
            (currentInfo.value.kind === '04' && !parentInfo.tempDeleteFlag)
          ) {
            item.disabled = false;
          } else {
            item.disabled = true;
          }
          if (currentInfo.value.tempDeleteFlag) {
            item.name = '取消临时删除';
          } else {
            item.name = '临时删除';
          }
        }
        if (item.children && !['batchDelete', 'noteList', 'MainList'].includes(item.code)) {
          item.disabled = false;
          item.children.forEach(childItem => {
            childItem.disabled = true;
            currentInfo.value.optionMenu.forEach(child => {
              if (child === childItem.code) {
                childItem.disabled = false;
              }
            });
          });
        }
        if (item.code === 'add') needAddQDandFB(item);

        handleNote(item, row);
        handleMainList(item, row);
      });
    });
    return true;
  },
});

const contextMenuClickEvent = async ({ menu, row }, args) => {
  console.log('🚀 ~ contextMenuClickEvent ~ menu:', menu);

  if (menu?.fatherCode === 'noteList' || menu.code == 'noteList') {
    if (menu.code != 'noteList') {
      handleNoteClick(menu, row);
    }
  } else if (menu?.fatherCode === 'MainList' || menu.code == 'MainList') {
    if (menu.code != 'MainList') {
      handleMainListClick(menu, row);
    }
  } else if (menu.code === 'delete') {
    deleteType();
  } else if (menu.code === 'cut') {
    cutFun();
  }
  if (menu.code === 'delBlan') {
    delBlankRowFun(0);
  }
  if (menu.code === 'copy') {
    // console.log(selectData.value.data);
    //和ctrl+v逻辑代码保持一致
    // const handleData =
    //   await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
    //     selectData.value.data
    //   );
    // selectData.value.data = handleData;
    // // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
    // if (handleData.length) {
    // selectData.value.isCopy = true;
    // }
    // copyFun();
    copyFun();
  } else if (menu.code === 'paste') {
    pasteFun();
  } else if (menu.code === 'lock') {
    if (row.isLocked === 1) {
      fbUnLockQd();
    } else {
      fbLockQd();
    }
  } else if (menu.code === 'lockPrice') {
    row.lockPriceFlag = !row.lockPriceFlag;
    updateFbData(row, 'lockPriceFlag');
  } else if (menu.code === 'pageColumnSetting') {
    showPageColumnSetting();
  } else if (menu.code === 'tempDelete') {
    updateDelTempStatusColl(selectState.selectedRowKeys);
  } else if (menu.code === 'copyCell') {
    copyClick(args);
  } else if (
    menu.code === 'batchDelete-child1' ||
    menu.code === 'batchDelete-child2' ||
    menu.code === 'batchDelete-child3'
  ) {
    if (menu.code == 'batchDelete-child3') {
      batchDeleteSubItemsVisible.value = true;
      batchDataType.value = menu.type;
    } else {
      batchDeleteVisible.value = true;
      batchDataType.value = menu.type;
    }
  } else if (menu.code === 'zcsbAdd') {
    rcjIndexVisible.value = true;
  } else if (['supplement-qd', 'supplement-de', 'supplement-rcj'].includes(menu.code)) {
    menu.activeKind = menu.kind;
    bcData(menu);
  } else if (menu.code !== 'add' && menu.kind && menu.code !== 'batchDelete') {
    addData(menu.kind);
  }
  hangZCSBMenuClick(menu, row);
};

const copyClick = (args, type = 'cell') => {
  if (type === 'cell') {
    copyData.value = [];
    stableRef.value.copySelectedRange();
    message.success('复制单元格成功');
  }
  args.hidePopup();
};
/**
 * 拖动了高度
 */
const dragHeight = h => {
  stableHeight.value = h - 50;
};
window.addEventListener('resize', function () {
  let tableEl = document.querySelector('.table-content');
  stableHeight.value = tableEl.clientHeight - 50;
});
// const rowClassName = ({ row }) => {
//   let ClassStr = 'normal-info';
//   if (row.kind === '0') {
//     ClassStr = 'row-unit';
//   } else if (row.kind === '01' || row.kind === '02') {
//     ClassStr = 'row-sub';
//   } else if (row.kind === '03') {
//     ClassStr = 'row-qd';
//   }
//   if (row.tempDeleteFlag) {
//     ClassStr = 'temp-delete';
//   }

//   if (
//     row.sequenceNbr ==
//     renderedList.value[renderedList.value.length - 1]?.sequenceNbr
//   ) {
//     ClassStr += ' last-row';
//   }
//   return ClassStr;
// };

const cellStyle = ({ row, column }) => {
  if (['bdCode'].includes(column.field)) {
    return {
      paddingLeft: row.customLevel * 12 + 'px',
    };
  }
};
const deNameRef = node => {
  // console.log(node.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode)
  return node.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode;
};
const headerClass = column => {
  if (column?.align === 'center') {
    return 'headerCenter';
  } else if (column?.align === 'right') {
    return 'headerRight';
  } else {
    return 'headerLeft';
  }
};

const cellClassName = ({ column, columnIndex, $columnIndex, row, rowIndex, $rowIndex }) => {
  let className = selectedClassName({ $columnIndex, row, column });
  if (column.field === 'bdCode') {
    className += ' code-color ' + `Virtual-pdLeft${row.customLevel} `;
  } else if (column.field === 'index') {
    className += ' index-bg';
  }

  // 批注提示
  if (column.field == 'name' && row?.annotations) {
    className += ' note-tips';
  }

  if (column.field === 'bdCode' && row.kind !== '04') {
    const line = row.maxLine || 1;
    className += ` cell-line-break-${line}`;
  }

  // 添加默认两行类名
  if (
    ['qfCode', 'measureType', 'description', 'costMajorName', 'projectAttr'].includes(
      column.field
    ) ||
    (column.field === 'bdCode' && row.kind === '04')
  ) {
    const line = row.maxLine || 2;
    className += ` cell-line-break-${line}`;
  }

  if (['projectAttr'].includes(column.field)) {
    className += ` projectAttr-item `;
  }

  return className;
};

// 编辑弹框关闭方法
const editCancel = () => {
  isShowModel.value = false;
};

const tableColumn = ref([
  { field: 'bdCodeLevel04', title: '项目编码' },
  { field: 'bdNameLevel04', title: '项目名称' },
  { field: 'unit', title: '单位' },
]);
const tableList = ref([]);

const keyupEvent = (row, value) => {
  if (row.kind !== '03') return;
  if (value.length > 1) {
    const $pulldown = pulldownRef.value;
    console.log('keyupEvent', $pulldown);
    if ($pulldown) {
      $pulldown?.showPanel();
    }
    searchQdByCode(value);
  }
};
const keyupAttrEvent = row => {
  if (row.kind !== '03' || row.projectAttr) return;
  const $pulldown = pulldownRefAttr.value;
  console.log('keyupEvent', $pulldown);
  if ($pulldown) {
    $pulldown?.togglePanel();
  }
};
const cellClickEvent = ({ row }) => {
  addCurrentInfo.value = row;
  const unit = Array.isArray(row.unit) ? row.unit : row.unit?.split('/');
  row.unit = unit;
  isNameOpen.value = true;
  const $pulldown = pulldownRef.value;
  if ($pulldown) {
    const $table = vexTable.value;
    if ($table) {
      isClearEdit.value = true;
      $table.clearEdit();
    }
    if (row.unit && row.unit.length > 1) {
      showUnitTooltip.value = true;
      showUnitTooltipType.value = 'code';
    } else {
      updateQdByCode(row.bdCodeLevel04, row.unit[0]);
    }
    $pulldown?.hidePanel();
    isClearEdit.value = false;
  }
};

// 根据编码模糊搜索标准清单
const searchQdByCode = code => {
  api.searchQdByCode({ code: code }).then(res => {
    if (res.status === 200 && res.result) {
      tableList.value = res.result;
    }
  });
};

// 通过标准编码插入清单
const updateQdByCode = (code, unit) => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    code: code,
    unit: unit,
    isSortQdCode: isSortQdCode.value,
  };
  api.updateQdByCode(apiData).then(res => {
    if (res.status === 200 && res.result) {
      selectUnit.value = '';
      if (currentInfo.value.standardId) {
        message.success('清单替换成功');
      } else {
        message.success('清单插入成功');
      }
      queryBranchDataById();
    }
  });
};

const saveData = inputData => {
  if (bdCode.value) {
    updateQdByPage(inputData);
  } else {
    addBcQdData(inputData);
  }
};

const deSaveData = inputData => {
  if (bdCode.value) {
    updateDeByPage(inputData);
  } else {
    addBcDeData(inputData);
  }
};

const rcjSaveData = inputData => {
  // (currentInfo.value.kind === '04' && currentInfo.value.rcjFlag === 0)
  if ([94, 95].includes(currentInfo.value?.kind)) {
    // 主材设备走人材机明细添加
    addMxqBcRcjData(inputData, currentInfo.value, bdCode.value, () => {
      rcjVisible.value = false;
      isUpdate.value = true;
      setTimeout(() => {
        isUpdate.value = false;
      }, 100);
    });
    return;
  }
  if (bdCode.value) {
    spRcjByPage(inputData);
  } else {
    addBjqBcRcjData(inputData);
  }
};

// 通过修改编码补充清单替换当前行数据
const updateQdByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
  };
  api.updateQdByPage(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

const updateDeByPage = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
  };
  api.updateDeByPage(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充子目新建成功');
      queryBranchDataById();
    }
  });
};
// 点击补充按钮补充清单数据
const addBcQdData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };

  api.addBcQdData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      qdVisible.value = false;
      message.success('补充清单新建成功');
      queryBranchDataById();
    }
  });
};

// 点击补充按钮补充定额数据
const addBcDeData = inputData => {
  let apiData = {
    unitId: projectStore.currentTreeInfo?.id,
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
    type: 1,
  };

  api.addBcDeData(apiData, inputData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      bdCode.value = '';
      deVisible.value = false;
      message.success('补充子目新建成功');
      queryBranchDataById();
    }
  });
};

// 分部分项 措施项目 补充界面替换人材机数据
const spRcjByPage = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentUpdateData.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    region: 0,
  };
  api.spRcjByPage(apiData).then(res => {
    addDataSequenceNbr.value = currentUpdateData.value.sequenceNbr;
    message.success('人材机替换成功');
    rcjVisible.value = false;
    queryBranchDataById();
  });
};
// 分部分项 措施项目 添加编辑区的人材机数据
const addBjqBcRcjData = inputData => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    pageInfo: JSON.parse(JSON.stringify(inputData)),
    type: 1,
    region: 0,
    rootLineId: projectStore.asideMenuCurrentInfo?.sequenceNbr,
  };
  api.addBjqBcRcjData(apiData).then(res => {
    if (res.status === 200 && res.result) {
      addDataSequenceNbr.value = res.result.data.sequenceNbr;
      page.value = Math.ceil((res.result.index + 1) / limit.value);
      rcjVisible.value = false;
      message.success('人材机新建成功');
      queryBranchDataById();
    }
  });
};
const showModel = type => {
  isPriceModel.value = true;
  switch (type) {
    case 'zscy':
      // 装饰垂运
      showModelType.value = 'zscy';
      showPriceTitle.value = '设置装饰垂运';
      break;
    case 'zscg':
      // 装饰超高
      showModelType.value = 'zscg';
      showPriceTitle.value = '设置装饰超高降效';
      break;
    case 'azfy':
      // 安装费用
      showModelType.value = 'azfy';
      showPriceTitle.value = '安装费用计取';
      break;
    case 'commercial':
      // 泵送增加费
      showModelType.value = 'commercial';
      showPriceTitle.value = '计算预拌混凝土泵送增加费';
      break;
    // case 'csfy':
    //   // 自动计算措施费用
    //   showModelType.value = 'csfy';
    //   showPriceTitle.value = '自动记取总价措施';
    //   break;
  }
};
// 记取费用数据更新
const updateData = async () => {
  isPriceModel.value = false;
  queryBranchDataById();
  queryFeeFileData();
  // await queryDjgcFileData();
  querySzType();
  // queryUnit();
};

const closePriceModel = () => {
  isPriceModel.value = false;
};

// 整体锁定
const allLock = () => {
  if (lockFlag.value) {
    fbUnLockAll();
  } else {
    fbLockAll();
  }
};

// 清单整体锁定
const fbLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      operateList.value.find(item => item.name === 'lock-subItem').label = '整体解锁';
      queryBranchDataById();
    }
  });
};

// 清单整体解锁
const fbUnLockAll = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.unitUnLockAll(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      operateList.value.find(item => item.name === 'lock-subItem').label = '整体锁定';
      queryBranchDataById();
    }
  });
};

// 清单锁定
const fbLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 1;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单锁定成功');
      queryBranchDataById();
    }
  });
};

// 清单解锁
const fbUnLockQd = () => {
  if (!showInfo()) return;
  currentInfo.value.isLocked = 0;
  let apiData = {
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
  };
  api.fbUnLockQd(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('清单解锁成功');
      queryBranchDataById();
    }
  });
};

// 补充页面点击取消操作 type 1 清单 2 定额 3 人材机
const bcCancel = type => {
  if (type === 1) {
    qdVisible.value = false;
  } else if (type === 2) {
    deVisible.value = false;
  } else {
    rcjVisible.value = false;
  }
  if (bdCode.value) {
    currentInfo.value.bdCode = currentInfo.value.originalBdCode;
  }
};

// 清单多单位时选择单位确定事件
const selectHandler = dataRef => {
  if (!selectUnit.value) {
    return message.warning('请选择单位');
  }
  showUnitTooltip.value = false;
  dataRef.unit = selectUnit.value;
  let obj = {
    bdCode: dataRef.bdCodeLevel04,
    name: dataRef.bdNameLevel04,
    sequenceNbr: dataRef.sequenceNbr,
    unit: dataRef.unit,
    quantityExpression: dataRef.quantityExpression,
    libraryCode: dataRef.libraryCode,
  };

  console.log('🚀 ~ selectHandler ~ name:', name);
  updateQdByCode(obj.bdCode, obj.unit);
};

// 批量删除
const delBatchData = (index = -1) => {
  // const posId = index > -1 ? getPrevOrNextDataByIndex(index)?.sequenceNbr : '';
  let ids = selectState.selectedRowKeys.filter(
    x => x !== projectStore.asideMenuCurrentInfo?.sequenceNbr
  );
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbrs: toRaw(ids),
  };
  console.log('批量删除', apiData);
  api.fbBatchDelete(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('批量删除成功');
      deleteVisible.value = false;
      deleteLoading.value = false;
      if (currentInfo.value.kind === '01' || currentInfo.value.kind === '02') {
        emits('updateMenuList');
      }
      queryBranchDataById();
    } else {
      message.error(res.message);
      deleteVisible.value = false;
      deleteLoading.value = false;
    }
  });
};

// 获取当前点击行下挂所有数据
/**
 * 以前的这里也修改了用户选择的数据，用户选择了数据，然后又在这掉接口了，不知道为啥，待以后优化吧
 */
const queryAllDataByBranchId = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    sequenceNbr: currentInfo.value?.sequenceNbr,
    pageSize: 10000,
    pageNum: page.value,
  };
  api.searchForSequenceNbr(apiData).then(async res => {
    if (res.status === 200 && res.result) {
      // 后端给的所有的数据，需要过滤。
      const handleData = await frameSelectRef.value.frameSelectJs.filterAndRemoveInvalid(
        res.result
      );
      selectData.value.data = handleData;
      // 点击index事件 ，因为以前走的是公共处理方法，经过过滤之后没有数据了，默认不能复制了，所以需要在调接口之后处理下
      if (handleData.length) {
        selectData.value.isCopy = true;
      }
    }
  });
};

const batchPasteQdDeData = () => {
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitId: projectStore.currentTreeInfo?.id,
    pointLine: JSON.parse(JSON.stringify(currentInfo.value)),
  };
  console.log('粘贴数据', apiData);
  api.fbBatchPaste(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('粘贴成功');
      queryBranchDataById();
    } else {
      message.error('粘贴失败');
    }
  });
};

// 分部分项定额上移下移
const moveDeData = ({ state, type }) => {
  console.log(state, type);
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    upId: projectStore.currentTreeInfo?.id,
    selectId: currentInfo.value.sequenceNbr,
    operateAction: state === 1 ? 'up' : 'down',
    type: 'fbfx',
  };
  if (type === 'move') {
    api.moveDeData(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
  if (type === 'level') {
    api.fbDataUpAndDownController(apiData).then(res => {
      if (res.status === 200 && res.result) {
        message.success('移动成功');
        emits('updateMenuList');
        addDataSequenceNbr.value = currentInfo.value.sequenceNbr;
        queryBranchDataById();
      }
    });
  }
};

// 组价方案匹配条件筛选
const filterData = val => {
  let tempList = [];
  tableData.value = [];
  if (val.length === 0 || !val) {
    tableData.value = originalTableData.value;
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  } else {
    originalTableData.value.forEach(item => {
      if (val.includes(item.matchStatus)) {
        tempList.push(item.sequenceNbr);
      }
    });
    for (let i = 0; i < originalTableData.value.length; i++) {
      if (
        tempList.includes(originalTableData.value[i].sequenceNbr) ||
        tempList.includes(originalTableData.value[i].parentId)
      ) {
        tableData.value.push(originalTableData.value[i]);
      }
    }
    tableData.value.forEach((item, index) => {
      item.index = (page.value - 1) * limit.value + (index + 1);
    });
  }
  // const initList = init(tableData.value);
  // nextTick(() => {
  //   initList();
  // });
};

const copyTableToClipboard = tableElement => {
  const rows = Array.from(tableElement.querySelectorAll('.multiple-check'));
  const theadRows = Array.from(tableElement.querySelectorAll('thead  tr'));
  const tempTable = document.createElement('table');
  const tempEl = document.createDocumentFragment();

  // 表格头部
  theadRows.forEach(row => {
    const tempRow = document.createElement('tr');
    const tds = row.querySelectorAll('th');
    if (tds.length) {
      tds.forEach(c => {
        const tempCell = document.createElement('td');
        tempCell.textContent = c.textContent.trim();
        tempCell.style.color = '#000';
        tempCell.style.fontWeight = 'bold';
        tempRow.appendChild(tempCell);
      });
    }
    tempRow.style.background = '#f3f3f3';

    tempEl.appendChild(tempRow);
  });

  rows.forEach(row => {
    const tempRow = document.createElement('tr');
    const tds = row.querySelectorAll('td');
    if (tds.length) {
      tds.forEach(c => {
        const tempCell = document.createElement('td');
        tempCell.textContent = c.textContent.trim();

        const color = window.getComputedStyle(c).getPropertyValue('color');
        if (color) {
          tempCell.style.color = color;
        }

        const childElement = c.querySelector('.code-flag');
        if (childElement) {
          tempCell.style.color = window.getComputedStyle(childElement).getPropertyValue('color');
        }

        tempRow.appendChild(tempCell);
      });

      // 行背景
      let bgColor = '#fff';
      let classLists = row.classList;
      if (classLists.contains('row-unit')) {
        bgColor = '#f0ecf2';
      } else if (classLists.contains('row-sub')) {
        bgColor = '#ececec';
      } else if (classLists.contains('row-qd')) {
        bgColor = '#f0f3fb';
      }
      if (bgColor) {
        tempRow.style.background = bgColor;
      }
    }

    tempEl.appendChild(tempRow);
  });

  tempTable.appendChild(tempEl);

  document.body.appendChild(tempTable);

  try {
    // 创建一个选择区域，选中临时元素内的所有内容
    const range = document.createRange();
    range.selectNodeContents(tempTable);
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    var aFileParts = [serializeFragment(range.cloneContents()).toString()];
    var oMyBlob = new Blob(aFileParts, { type: 'text/html' });
    navigator.clipboard
      .write([
        new ClipboardItem({
          'text/html': oMyBlob,
        }),
      ])
      .then(() => {
        console.log('HTML content copied to clipboard.');
      });

    // navigator.clipboard.writeText(selection.toString());
    selection.removeAllRanges();
  } catch (err) {
    console.error('Failed to copy table:', err);
  } finally {
    // 移除临时元素
    document.body.removeChild(tempTable);
  }
};

const serializeFragment = fragment => {
  const div = document.createElement('div');
  div.appendChild(fragment);
  return `<table border="1"  cellspacing="0" cellpadding="0">${div.innerHTML}</table>`;
};

// 复用组价
let ReuseGroupPriceRef = ref(null);

const openReuseGroupPrice = ({ activeKind }) => {
  console.log('🚀 ~', activeKind);
  if (!ReuseGroupPriceRef.value) {
    console.log('🚀 ~ openReuseGroupPrice ~ ReuseGroupPriceRef:');
    nextTick(() => {
      openReuseGroupPrice({ activeKind });
    });
    return;
  }
  if ([0, 1, 2].includes(activeKind)) {
    ReuseGroupPriceRef.value.open(activeKind);
  }
};

// 清单快速组价
const qdQuickPricingRef = ref(null);
const openQdQuickPricing = () => {
  qdQuickPricingRef.value.open('fbfx');
};

const standardGroupRef = ref(null);
const openStandardGroupPrice = () => {
  standardGroupRef.value.open('fbfx');
};

const colorStateFn = () => {
  let currentOperate = operateList.value.find(item => item.name === 'select-color');
  if ([94, 95].includes(currentInfo.value?.kind)) {
    currentOperate.options.forEach(item => {
      item.isValid = false;
    });
  } else {
    currentOperate.options.forEach(item => {
      item.isValid = true;
    });
  }
};

const updateDataColorColl = data => {
  let tempList = [];
  tableData.value.forEach(item => {
    if (selectState.selectedRowKeys.includes(item.sequenceNbr)) {
      tempList.push(item);
    }
  });
  let ids = [];
  tempList.forEach(item => {
    if (![94, 95].includes(item.kind)) {
      ids.push(item.sequenceNbr);
    }
  });
  let apiData = {
    constructId: projectStore.currentTreeGroupInfo?.constructId,
    singleId: projectStore.currentTreeGroupInfo?.singleId,
    unitWorkId: projectStore.currentTreeInfo?.id,
    idList: JSON.parse(JSON.stringify(ids)),
    column: 'color',
    value: data.activeKind,
  };
  console.log('apiData颜色设置', apiData);
  api.updateDataColorColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      message.success('颜色设置成功');
      projectStore.isRefreshProjectTree = true;
      queryBranchDataById();
    }
  });
};
const costMajorNameChange = row => {
  //取费文件变更
  // console.log('costMajorNameChange');
  // let target = feeFileList.value.find(i => i.qfName === row.costMajorName);
  // row.qfCode = target.qfCode; //编辑区内切换【取费文件】时会默认带出与其关联的标准模板的【单价构成文件】
};
const qfCodeChange = row => {
  //单价构成文件变更--
  // let target = djgcFileList.value.find(i => i.qfCode === row.qfCode);
  // if (!(target.qfName.indexOf('_') === -1)) return; //仿制模板变更不影响取费文件对应字段变更
  // row.costMajorName = target.qfName; //编辑区内切换【取费文件】时会默认带出与其关联的标准模板的【单价构成文件】
};
const {
  showColumns,
  handlerColumns,
  closeColumn,
  initColumns,
  updateColumns,
  getDefaultColumns,
  setHeaderCellClassName,
} = useFormatTableColumns({
  type: 'ys',
  initCallback: () => {
    renderLine();
  },
  initColumnsCallback: () => {
    initColumns({
      columns: getTableColumns(emits, 'fbfx'),
      pageName: 'fbfx',
    });
  },
});

// 页面列设置
let columnSettingRef = ref();
const showPageColumnSetting = () => {
  columnSettingRef.value.open();
};

defineExpose({
  queryBranchDataById,
  copyAndPaste,
  posRow,
  showPageColumnSetting,
});
</script>

<style lang="scss" src="@/assets/css/subItemProject.scss" scoped></style>
<style lang="scss" scoped>
@use './s-table.scss';
.association-selected {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 100;
}
.normal-info .code-flag {
  color: #a73d3d;
}
.code-black {
  color: #2a2a2a;
}
.flag-green {
  background: #90c942;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-orange {
  background: #ffaa09;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}
.flag-red {
  background: #de3f3f;
  opacity: 1;
  border-radius: 4px;
  color: #ffffff;
  padding: 0px 2px;
  margin-right: 3px;
  font-size: 11px;
}

.multiple-select {
  width: 35px;
  height: 16px;
  line-height: 16px;
  margin-left: -10px;
  //text-indent: 10px;
  cursor: pointer;
}
::v-deep(.vxe-pulldown--panel) {
  z-index: 999 !important;
}
.subItem-project {
  height: 100%;
  .head {
    display: flex;
    align-items: center;
    // height: 40px;
    padding: 0 10px;
  }
  .table-content {
    height: 100%;
    //user-select: none;
    ::v-deep(.vxe-table) {
      // .vxe-cell--tree-node{
      //   // left: 0!important;
      //   padding-left: 0!important;
      // }
    }
    ::v-deep(.vxe-table .row-unit) {
      background: #e6dbeb;
    }
    ::v-deep(.vxe-table .row-sub) {
      background: #efe9f2;
    }
    ::v-deep(.vxe-table .row-qd) {
      background: #dce6fa;
    }
    ::v-deep(.vxe-body--row.row--current) {
      background: #a6c3fa;
    }
    ::v-deep(.vxe-table .row-qd .code-color) {
      color: #ce2929;
    }
    ::v-deep(.vxe-table .normal-info .code-color) {
      color: #ce2929;
      .vxe-tree-cell {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        //max-height: 3.0em; /* 高度为字体大小的两倍 */
        //line-height: 1.5em; /* 行高 */
        //height: auto; /* 高度为行高的两倍 */
      }
    }
    ::v-deep(.vxe-table .index-bg) {
      background-color: #ffffff;
    }
    ::v-deep(.vxe-table .temp-delete) {
      background: #f3f2f3;
      color: #a7a7a7;
      text-decoration: line-through;
    }
    ::v-deep(.surely-table-header-cell):hover {
      .icon-close-s {
        opacity: 1;
      }
    }
  }
  .quota-content {
    height: 100%;
    //overflow: auto;
    //user-select: none;
    // user-drag: none;
  }
  .project-attr {
    white-space: pre-wrap;
    text-align: left;
  }
}

.ceilingPrice-wrap {
  ::v-deep(.vxe-input--extra-suffix) {
    display: none;
  }
  ::v-deep(.vxe-input--inner) {
    padding-right: 0 !important;
  }
}

.content {
  margin-bottom: 20px;
}
.edit-content {
  margin-bottom: 20px;
}
.my-dropdown4 {
  width: 600px;
  height: 120px;
  overflow: auto;
  background-color: #fff;
  box-shadow: 0 0 6px 2px rgba(0, 0, 0, 0.1);
}
.unit-radio {
  position: absolute;
  top: 50%;
  right: 50%;
  z-index: 999;
  padding: 15px;
  box-shadow: 0 0 15px -6px rgba(0, 0, 0, 0.4);
  width: 120px;
  background: #fff;
  .title {
    color: #333;
  }
}
.code {
  margin-left: 8px;
  padding: 6px 4px;
}
</style>
