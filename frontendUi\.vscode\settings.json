{
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.formatOnSave": true, // 保存文件时自动格式化
  "editor.codeActionsOnSave": {
    "source.fixAll": "always", // 自动修复所有可修复的错误
    "source.organizeImports": "never" // 整理导入语句
  },
  "eslint.useFlatConfig": true,
  "eslint.enable": false,
  "eslint.validate": [
    "javascript",
    "vue",
    "vue-html",
    "typescript",
    "typescriptreact",
    "html",
    "css",
    "scss",
    "less",
    "json",
    "jsonc",
    "json5",
    "markdown"
  ],
  "eslint.options": {
    "overrideConfigFile": "eslint.config.js" // 使用本地ESLint配置
  },
  "prettier.configPath": ".prettierrc.json"
}
